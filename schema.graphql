# This file is automatically generated by Keystone, do not modify it manually.
# Modify your Keystone config when you want to change this.

type User {
  id: ID!
  name: String
  email: String
  password: PasswordState
  role: Role
  tasks(where: TodoWhereInput! = {}, orderBy: [TodoOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TodoWhereUniqueInput): [Todo!]
  tasksCount(where: TodoWhereInput! = {}): Int
}

type PasswordState {
  isSet: Boolean!
}

input UserWhereUniqueInput {
  id: ID
}

input UserWhereInput {
  AND: [UserWhereInput!]
  OR: [UserWhereInput!]
  NOT: [UserWhereInput!]
  id: IDFilter
  name: StringFilter
  role: RoleWhereInput
  tasks: TodoManyRelationFilter
}

input IDFilter {
  equals: ID
  in: [ID!]
  notIn: [ID!]
  lt: ID
  lte: ID
  gt: ID
  gte: ID
  not: IDFilter
}

input StringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode
  not: NestedStringFilter
}

enum QueryMode {
  default
  insensitive
}

input NestedStringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input TodoManyRelationFilter {
  every: TodoWhereInput
  some: TodoWhereInput
  none: TodoWhereInput
}

input UserOrderByInput {
  id: OrderDirection
  name: OrderDirection
}

enum OrderDirection {
  asc
  desc
}

input UserUpdateInput {
  name: String
  email: String
  password: String
  role: RoleRelateToOneForUpdateInput
  tasks: TodoRelateToManyForUpdateInput
}

input RoleRelateToOneForUpdateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
  disconnect: Boolean
}

input TodoRelateToManyForUpdateInput {
  disconnect: [TodoWhereUniqueInput!]
  set: [TodoWhereUniqueInput!]
  create: [TodoCreateInput!]
  connect: [TodoWhereUniqueInput!]
}

input UserUpdateArgs {
  where: UserWhereUniqueInput!
  data: UserUpdateInput!
}

input UserCreateInput {
  name: String
  email: String
  password: String
  role: RoleRelateToOneForCreateInput
  tasks: TodoRelateToManyForCreateInput
}

input RoleRelateToOneForCreateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
}

input TodoRelateToManyForCreateInput {
  create: [TodoCreateInput!]
  connect: [TodoWhereUniqueInput!]
}

type Role {
  id: ID!
  name: String
  canCreateTodos: Boolean
  canManageAllTodos: Boolean
  canSeeOtherPeople: Boolean
  canEditOtherPeople: Boolean
  canManagePeople: Boolean
  canManageRoles: Boolean
  canAccessDashboard: Boolean
  assignedTo(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  assignedToCount(where: UserWhereInput! = {}): Int
}

input RoleWhereUniqueInput {
  id: ID
}

input RoleWhereInput {
  AND: [RoleWhereInput!]
  OR: [RoleWhereInput!]
  NOT: [RoleWhereInput!]
  id: IDFilter
  name: StringFilter
  canCreateTodos: BooleanFilter
  canManageAllTodos: BooleanFilter
  canSeeOtherPeople: BooleanFilter
  canEditOtherPeople: BooleanFilter
  canManagePeople: BooleanFilter
  canManageRoles: BooleanFilter
  canAccessDashboard: BooleanFilter
  assignedTo: UserManyRelationFilter
}

input BooleanFilter {
  equals: Boolean
  not: BooleanFilter
}

input UserManyRelationFilter {
  every: UserWhereInput
  some: UserWhereInput
  none: UserWhereInput
}

input RoleOrderByInput {
  id: OrderDirection
  name: OrderDirection
  canCreateTodos: OrderDirection
  canManageAllTodos: OrderDirection
  canSeeOtherPeople: OrderDirection
  canEditOtherPeople: OrderDirection
  canManagePeople: OrderDirection
  canManageRoles: OrderDirection
  canAccessDashboard: OrderDirection
}

input RoleUpdateInput {
  name: String
  canCreateTodos: Boolean
  canManageAllTodos: Boolean
  canSeeOtherPeople: Boolean
  canEditOtherPeople: Boolean
  canManagePeople: Boolean
  canManageRoles: Boolean
  canAccessDashboard: Boolean
  assignedTo: UserRelateToManyForUpdateInput
}

input UserRelateToManyForUpdateInput {
  disconnect: [UserWhereUniqueInput!]
  set: [UserWhereUniqueInput!]
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

input RoleUpdateArgs {
  where: RoleWhereUniqueInput!
  data: RoleUpdateInput!
}

input RoleCreateInput {
  name: String
  canCreateTodos: Boolean
  canManageAllTodos: Boolean
  canSeeOtherPeople: Boolean
  canEditOtherPeople: Boolean
  canManagePeople: Boolean
  canManageRoles: Boolean
  canAccessDashboard: Boolean
  assignedTo: UserRelateToManyForCreateInput
}

input UserRelateToManyForCreateInput {
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

type Todo {
  id: ID!
  label: String
  description: Todo_description_Document
  isComplete: Boolean
  isPrivate: Boolean
  priority: Int
  largeNumber: BigInt
  weight: Float
  budget: Decimal
  status: String
  dueDate: DateTime
  metadata: JSON
  secretNote: PasswordState
  assignedTo: User
}

type Todo_description_Document {
  document(hydrateRelationships: Boolean! = false): JSON!
}

scalar BigInt

scalar Decimal

scalar DateTime @specifiedBy(url: "https://datatracker.ietf.org/doc/html/rfc3339#section-5.6")

input TodoWhereUniqueInput {
  id: ID
}

input TodoWhereInput {
  AND: [TodoWhereInput!]
  OR: [TodoWhereInput!]
  NOT: [TodoWhereInput!]
  id: IDFilter
  label: StringFilter
  isComplete: BooleanFilter
  isPrivate: BooleanFilter
  priority: IntNullableFilter
  largeNumber: BigIntNullableFilter
  weight: FloatNullableFilter
  budget: DecimalNullableFilter
  status: StringNullableFilter
  dueDate: DateTimeNullableFilter
  secretNote: PasswordFilter
  assignedTo: UserWhereInput
}

input IntNullableFilter {
  equals: Int
  in: [Int!]
  notIn: [Int!]
  lt: Int
  lte: Int
  gt: Int
  gte: Int
  not: IntNullableFilter
}

input BigIntNullableFilter {
  equals: BigInt
  in: [BigInt!]
  notIn: [BigInt!]
  lt: BigInt
  lte: BigInt
  gt: BigInt
  gte: BigInt
  not: BigIntNullableFilter
}

input FloatNullableFilter {
  equals: Float
  in: [Float!]
  notIn: [Float!]
  lt: Float
  lte: Float
  gt: Float
  gte: Float
  not: FloatNullableFilter
}

input DecimalNullableFilter {
  equals: Decimal
  in: [Decimal!]
  notIn: [Decimal!]
  lt: Decimal
  lte: Decimal
  gt: Decimal
  gte: Decimal
  not: DecimalNullableFilter
}

input StringNullableFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode
  not: StringNullableFilter
}

input DateTimeNullableFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeNullableFilter
}

input PasswordFilter {
  isSet: Boolean!
}

input TodoOrderByInput {
  id: OrderDirection
  label: OrderDirection
  isComplete: OrderDirection
  isPrivate: OrderDirection
  priority: OrderDirection
  largeNumber: OrderDirection
  weight: OrderDirection
  budget: OrderDirection
  status: OrderDirection
  dueDate: OrderDirection
}

input TodoUpdateInput {
  label: String
  description: JSON
  isComplete: Boolean
  isPrivate: Boolean
  priority: Int
  largeNumber: BigInt
  weight: Float
  budget: Decimal
  status: String
  dueDate: DateTime
  metadata: JSON
  secretNote: String
  assignedTo: UserRelateToOneForUpdateInput
}

input UserRelateToOneForUpdateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
  disconnect: Boolean
}

input TodoUpdateArgs {
  where: TodoWhereUniqueInput!
  data: TodoUpdateInput!
}

input TodoCreateInput {
  label: String
  description: JSON
  isComplete: Boolean
  isPrivate: Boolean
  priority: Int
  largeNumber: BigInt
  weight: Float
  budget: Decimal
  status: String
  dueDate: DateTime
  metadata: JSON
  secretNote: String
  assignedTo: UserRelateToOneForCreateInput
}

input UserRelateToOneForCreateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type Mutation {
  createUser(data: UserCreateInput!): User
  createUsers(data: [UserCreateInput!]!): [User]
  updateUser(where: UserWhereUniqueInput!, data: UserUpdateInput!): User
  updateUsers(data: [UserUpdateArgs!]!): [User]
  deleteUser(where: UserWhereUniqueInput!): User
  deleteUsers(where: [UserWhereUniqueInput!]!): [User]
  createRole(data: RoleCreateInput!): Role
  createRoles(data: [RoleCreateInput!]!): [Role]
  updateRole(where: RoleWhereUniqueInput!, data: RoleUpdateInput!): Role
  updateRoles(data: [RoleUpdateArgs!]!): [Role]
  deleteRole(where: RoleWhereUniqueInput!): Role
  deleteRoles(where: [RoleWhereUniqueInput!]!): [Role]
  createTodo(data: TodoCreateInput!): Todo
  createTodos(data: [TodoCreateInput!]!): [Todo]
  updateTodo(where: TodoWhereUniqueInput!, data: TodoUpdateInput!): Todo
  updateTodos(data: [TodoUpdateArgs!]!): [Todo]
  deleteTodo(where: TodoWhereUniqueInput!): Todo
  deleteTodos(where: [TodoWhereUniqueInput!]!): [Todo]
  endSession: Boolean!
  authenticateUserWithPassword(email: String!, password: String!): UserAuthenticationWithPasswordResult
  createInitialUser(data: CreateInitialUserInput!): UserAuthenticationWithPasswordSuccess!
}

union UserAuthenticationWithPasswordResult = UserAuthenticationWithPasswordSuccess | UserAuthenticationWithPasswordFailure

type UserAuthenticationWithPasswordSuccess {
  sessionToken: String!
  item: User!
}

type UserAuthenticationWithPasswordFailure {
  message: String!
}

input CreateInitialUserInput {
  name: String
  email: String
  password: String
}

type Query {
  user(where: UserWhereUniqueInput!): User
  users(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  usersCount(where: UserWhereInput! = {}): Int
  role(where: RoleWhereUniqueInput!): Role
  roles(where: RoleWhereInput! = {}, orderBy: [RoleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RoleWhereUniqueInput): [Role!]
  rolesCount(where: RoleWhereInput! = {}): Int
  todo(where: TodoWhereUniqueInput!): Todo
  todos(where: TodoWhereInput! = {}, orderBy: [TodoOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TodoWhereUniqueInput): [Todo!]
  todosCount(where: TodoWhereInput! = {}): Int
  keystone: KeystoneMeta!
  authenticatedItem: AuthenticatedItem
  redirectToInit: Boolean
}

union AuthenticatedItem = User

type KeystoneMeta {
  adminMeta: KeystoneAdminMeta!
}

type KeystoneAdminMeta {
  lists: [KeystoneAdminUIListMeta!]!
  list(key: String!): KeystoneAdminUIListMeta
}

type KeystoneAdminUIListMeta {
  key: String!
  path: String!
  label: String!
  singular: String!
  plural: String!
  description: String
  pageSize: Int!
  labelField: String!
  fields: [KeystoneAdminUIFieldMeta!]!
  groups: [KeystoneAdminUIFieldGroupMeta!]!
  graphql: KeystoneAdminUIGraphQL!
  initialColumns: [String!]!
  initialSearchFields: [String!]!
  initialSort: KeystoneAdminUISort
  isSingleton: Boolean!
  hideCreate: Boolean!
  hideDelete: Boolean!
  isHidden: Boolean!
  itemQueryName: String!
  listQueryName: String!
}

type KeystoneAdminUIFieldMeta {
  path: String!
  label: String!
  description: String
  isOrderable: Boolean!
  isFilterable: Boolean!
  isNonNull: [KeystoneAdminUIFieldMetaIsNonNull!]
  fieldMeta: JSON
  viewsIndex: Int!
  customViewsIndex: Int
  createView: KeystoneAdminUIFieldMetaCreateView!
  listView: KeystoneAdminUIFieldMetaListView!
  itemView(id: ID): KeystoneAdminUIFieldMetaItemView
  search: QueryMode
}

enum KeystoneAdminUIFieldMetaIsNonNull {
  read
  create
  update
}

type KeystoneAdminUIFieldMetaCreateView {
  fieldMode: KeystoneAdminUIFieldMetaCreateViewFieldMode!
}

enum KeystoneAdminUIFieldMetaCreateViewFieldMode {
  edit
  hidden
}

type KeystoneAdminUIFieldMetaListView {
  fieldMode: KeystoneAdminUIFieldMetaListViewFieldMode!
}

enum KeystoneAdminUIFieldMetaListViewFieldMode {
  read
  hidden
}

type KeystoneAdminUIFieldMetaItemView {
  fieldMode: KeystoneAdminUIFieldMetaItemViewFieldMode
  fieldPosition: KeystoneAdminUIFieldMetaItemViewFieldPosition
}

enum KeystoneAdminUIFieldMetaItemViewFieldMode {
  edit
  read
  hidden
}

enum KeystoneAdminUIFieldMetaItemViewFieldPosition {
  form
  sidebar
}

type KeystoneAdminUIFieldGroupMeta {
  label: String!
  description: String
  fields: [KeystoneAdminUIFieldMeta!]!
}

type KeystoneAdminUIGraphQL {
  names: KeystoneAdminUIGraphQLNames!
}

type KeystoneAdminUIGraphQLNames {
  outputTypeName: String!
  whereInputName: String!
  whereUniqueInputName: String!
  createInputName: String!
  createMutationName: String!
  createManyMutationName: String!
  relateToOneForCreateInputName: String!
  relateToManyForCreateInputName: String!
  itemQueryName: String!
  listOrderName: String!
  listQueryCountName: String!
  listQueryName: String!
  updateInputName: String!
  updateMutationName: String!
  updateManyInputName: String!
  updateManyMutationName: String!
  relateToOneForUpdateInputName: String!
  relateToManyForUpdateInputName: String!
  deleteMutationName: String!
  deleteManyMutationName: String!
}

type KeystoneAdminUISort {
  field: String!
  direction: KeystoneAdminUISortDirection!
}

enum KeystoneAdminUISortDirection {
  ASC
  DESC
}
