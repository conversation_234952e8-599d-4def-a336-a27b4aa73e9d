This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.
The content has been processed where security check has been disabled.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: packages/core/src/fields/types
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
packages/
  core/
    src/
      fields/
        types/
          bigInt/
            views/
              index.tsx
            index.ts
          bytes/
            views/
              index.tsx
            index.ts
          calendarDay/
            views/
              index.tsx
            index.ts
          checkbox/
            views/
              index.tsx
            index.ts
          decimal/
            views/
              index.tsx
            index.ts
          file/
            views/
              Field.tsx
              index.tsx
            index.ts
          float/
            views/
              index.tsx
            index.ts
          image/
            views/
              Field.tsx
              index.tsx
            index.ts
            internal-utils.ts
            utils.ts
          integer/
            views/
              index.tsx
            index.ts
          json/
            views/
              index.tsx
            index.ts
          multiselect/
            views/
              index.tsx
            index.ts
          password/
            views/
              index.tsx
            index.ts
          relationship/
            views/
              ComboboxMany.tsx
              ComboboxSingle.tsx
              ContextualActions.tsx
              index.tsx
              RelationshipTable.tsx
              types.ts
              useApolloQuery.ts
              useFilter.tsx
            index.ts
          select/
            views/
              index.tsx
              SegmentedControl.tsx
            index.ts
          text/
            views/
              index.tsx
            index.ts
          timestamp/
            views/
              __tests__/
                index.tsx
                utils.tsx
              index.tsx
              utils.ts
            index.ts
          virtual/
            views/
              index.tsx
            index.ts
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="packages/core/src/fields/types/bigInt/views/index.tsx">
import { useState } from 'react'

import { ContextualHelp } from '@keystar/ui/contextual-help'
import { Content } from '@keystar/ui/slots'
import { TextField } from '@keystar/ui/text-field'
import { Heading, Text } from '@keystar/ui/typography'

import type { SimpleFieldTypeInfo } from '../../../../types'
import {
  type FieldController,
  type FieldControllerConfig,
  type FieldProps,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'

const TYPE_OPERATOR_MAP = {
  equals: '=',
  not: '≠',
  gt: '>',
  lt: '<',
  gte: '≥',
  lte: '≤',
} as const

type Value =
  | { kind: 'create'; value: string | null }
  | { kind: 'update'; initial: string | null; value: string | null }

type Validation = {
  min: string
  max: string
}

function validate_(
  value: Value,
  validation: Validation,
  isRequired: boolean,
  label: string,
  hasAutoIncrementDefault: boolean
): string | undefined {
  const { value: input, kind } = value
  if (kind === 'create' && hasAutoIncrementDefault && input === null) return
  if (kind === 'update' && value.initial === null && input === null) return
  if (isRequired && input === null) return `${label} is required`
  if (typeof input !== 'string') return
  try {
    const v = BigInt(input)
    if (validation.min !== undefined && v < BigInt(validation.min))
      return `${label} must be greater than or equal to ${validation.min}`
    if (validation.max !== undefined && v > BigInt(validation.max))
      return `${label} must be less than or equal to ${validation.max}`
  } catch (e: any) {
    return `${label} is not a valid BigInt`
  }
}

export function controller(
  config: FieldControllerConfig<{
    validation: Validation
    defaultValue: string | null | 'autoincrement'
  }>
): FieldController<Value, string | null, SimpleFieldTypeInfo<'BigInt'>['inputs']['where']> & {
  validation: Validation
  hasAutoIncrementDefault: boolean
} {
  const validate = (value: Value, opts: { isRequired: boolean }) => {
    return validate_(
      value,
      config.fieldMeta.validation,
      opts.isRequired,
      config.label,
      config.fieldMeta.defaultValue === 'autoincrement'
    )
  }

  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    validation: config.fieldMeta.validation,
    defaultValue: {
      kind: 'create',
      value:
        config.fieldMeta.defaultValue === 'autoincrement' ? null : config.fieldMeta.defaultValue,
    },
    deserialize: data => ({ kind: 'update', value: data[config.path], initial: data[config.path] }),
    serialize: value => ({ [config.path]: value.value }),
    filter: {
      Filter(props) {
        const {
          autoFocus,
          context,
          forceValidation,
          typeLabel,
          onChange,
          type,
          value,
          ...otherProps
        } = props
        const [isDirty, setDirty] = useState(false)
        if (type === 'empty' || type === 'not_empty') return null

        const labelProps =
          context === 'add' ? { label: config.label, description: typeLabel } : { label: typeLabel }

        return (
          <TextField
            {...otherProps}
            {...labelProps}
            autoFocus={autoFocus}
            errorMessage={
              (forceValidation || isDirty) &&
              !validate({ kind: 'update', initial: null, value }, { isRequired: true })
                ? 'Required'
                : null
            }
            inputMode="numeric"
            width="auto"
            onBlur={() => setDirty(true)}
            onChange={x => onChange?.(x === '' ? null : x)}
            value={value ?? ''}
          />
        )
      },

      graphql: ({ type, value }) => {
        if (type === 'empty') return { [config.path]: { equals: null } }
        if (type === 'not_empty') return { [config.path]: { not: { equals: null } } }
        if (type === 'not') return { [config.path]: { not: { equals: value } } }
        return { [config.path]: { [type]: value } }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (type === 'equals' && value === null) {
            return [{ type: 'empty', value: null }]
          }
          if (!value) return []
          if (type === 'equals') return { type: 'equals', value: value as unknown as string }
          if (type === 'not') {
            if (value?.equals === null) return { type: 'not_empty', value: null }
            return { type: 'not', value: value.equals as unknown as string }
          }
          if (type === 'gt' || type === 'gte' || type === 'lt' || type === 'lte') {
            return { type, value: value as unknown as string }
          }
          return []
        })
      },
      Label({ label, type, value }) {
        if (type === 'empty' || type === 'not_empty') return label.toLocaleLowerCase()
        const operator = TYPE_OPERATOR_MAP[type as keyof typeof TYPE_OPERATOR_MAP]
        return `${operator} ${value}`
      },
      types: {
        equals: {
          label: 'Is exactly',
          initialValue: null,
        },
        not: {
          label: 'Is not exactly',
          initialValue: null,
        },
        gt: {
          label: 'Is greater than',
          initialValue: null,
        },
        lt: {
          label: 'Is less than',
          initialValue: null,
        },
        gte: {
          label: 'Is greater than or equal to',
          initialValue: null,
        },
        lte: {
          label: 'Is less than or equal to',
          initialValue: null,
        },
        empty: {
          label: 'Is empty',
          initialValue: null,
        },
        not_empty: {
          label: 'Is not empty',
          initialValue: null,
        },
      },
    },

    hasAutoIncrementDefault: config.fieldMeta.defaultValue === 'autoincrement',
    validate: (value, opts) => validate(value, opts) === undefined,
  }
}

export function Field({
  field,
  value,
  onChange,
  autoFocus,
  forceValidation,
  isRequired,
}: FieldProps<typeof controller>) {
  const [isDirty, setDirty] = useState(false)
  const isReadOnly = !onChange || field.hasAutoIncrementDefault

  if (field.hasAutoIncrementDefault && value.kind === 'create') {
    return (
      <TextField
        autoFocus={autoFocus}
        description={field.description}
        label={field.label}
        isReadOnly
        defaultValue="--"
        contextualHelp={
          <ContextualHelp>
            <Heading>Auto increment</Heading>
            <Content>
              <Text>
                This field is set to auto increment. It will default to the next available number.
              </Text>
            </Content>
          </ContextualHelp>
        }
      />
    )
  }

  const validate = (value: Value) => {
    return validate_(
      value,
      field.validation,
      isRequired,
      field.label,
      field.hasAutoIncrementDefault
    )
  }

  return (
    <TextField
      autoFocus={autoFocus}
      description={field.description}
      label={field.label}
      errorMessage={(forceValidation || isDirty) && validate(value)}
      isReadOnly={isReadOnly}
      isRequired={isRequired}
      inputMode="numeric"
      width="alias.singleLineWidth"
      onBlur={() => setDirty(true)}
      onChange={x => onChange?.({ ...value, value: x === '' ? null : x })}
      value={value.value ?? ''}
    />
  )
}
</file>

<file path="packages/core/src/fields/types/bigInt/index.ts">
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { filters } from '../../filters'
import { resolveDbNullable, makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import type { controller } from './views'

export type BigIntFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'BigInt'>
> & {
  isIndexed?: boolean | 'unique'
  defaultValue?: bigint | null | { kind: 'autoincrement' }
  validation?: {
    isRequired?: boolean
    min?: bigint
    max?: bigint
  }
  db?: {
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
  }
}

// for a signed 64-bit integer
const MAX_INT = 9223372036854775807n
const MIN_INT = -9223372036854775808n

// TODO: https://github.com/Thinkmill/keystatic/blob/main/design-system/pkg/src/number-field/NumberField.tsx
export function bigInt<ListTypeInfo extends BaseListTypeInfo>(
  config: BigIntFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { defaultValue: defaultValue_ = null, isIndexed, validation = {} } = config

  const { isRequired = false, min, max } = validation
  const defaultValue =
    typeof defaultValue_ === 'bigint' ? defaultValue_ : (defaultValue_?.kind ?? null)

  return meta => {
    if (defaultValue === 'autoincrement') {
      if (meta.provider === 'sqlite' || meta.provider === 'mysql') {
        throw new Error(
          `${meta.listKey}.${meta.fieldKey} specifies defaultValue: { kind: 'autoincrement' }, this is not supported on ${meta.provider}`
        )
      }
      const isNullable = resolveDbNullable(validation, config.db)
      if (isNullable !== false) {
        throw new Error(
          `${meta.listKey}.${meta.fieldKey} specifies defaultValue: { kind: 'autoincrement' } but doesn't specify db.isNullable: false.\n` +
            `Having nullable autoincrements on Prisma currently incorrectly creates a non-nullable column so it is not allowed.\n` +
            `https://github.com/prisma/prisma/issues/8663`
        )
      }
      if (isRequired) {
        throw new Error(
          `${meta.listKey}.${meta.fieldKey} defaultValue: { kind: 'autoincrement' } conflicts with validation.isRequired: true`
        )
      }
    }
    if (
      defaultValue !== null &&
      defaultValue !== 'autoincrement' &&
      typeof defaultValue !== 'bigint'
    ) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a default value of: ${defaultValue} but it must be a valid finite number`
      )
    }
    if (min !== undefined && !Number.isInteger(min)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.min: ${min} but it must be an integer`
      )
    }
    if (max !== undefined && !Number.isInteger(max)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.max: ${max} but it must be an integer`
      )
    }
    if (min !== undefined && (min > MAX_INT || min < MIN_INT)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.min: ${min} which is outside of the range of a 64-bit signed integer`
      )
    }
    if (max !== undefined && (max > MAX_INT || max < MIN_INT)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.max: ${max} which is outside of the range of a 64-bit signed integer`
      )
    }
    if (min !== undefined && max !== undefined && min > max) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a validation.max that is less than the validation.min, and therefore has no valid options`
      )
    }

    const hasAdditionalValidation = min !== undefined || max !== undefined
    const { mode, validate } = makeValidateHook(
      meta,
      config,
      hasAdditionalValidation
        ? ({ resolvedData, operation, addValidationError }) => {
            if (operation === 'delete') return

            const value = resolvedData[meta.fieldKey]
            if (typeof value === 'number') {
              if (min !== undefined && value < min) {
                addValidationError(`value must be greater than or equal to ${min}`)
              }

              if (max !== undefined && value > max) {
                addValidationError(`value must be less than or equal to ${max}`)
              }
            }
          }
        : undefined
    )

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: 'BigInt',
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      default:
        typeof defaultValue === 'bigint'
          ? { kind: 'literal', value: defaultValue }
          : defaultValue === 'autoincrement'
            ? { kind: 'autoincrement' }
            : undefined,
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, isRequired),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.BigInt }) } : undefined,
        where: {
          arg: g.arg({ type: filters[meta.provider].BigInt[mode] }),
          resolve: mode === 'optional' ? filters.resolveCommon : undefined,
        },
        create: {
          arg: g.arg({
            type: g.BigInt,
            defaultValue: typeof defaultValue === 'bigint' ? defaultValue : undefined,
          }),
          resolve(value) {
            if (value === undefined) {
              if (defaultValue === 'autoincrement') return null
              return defaultValue
            }
            return value
          },
        },
        update: { arg: g.arg({ type: g.BigInt }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({ type: g.BigInt }),
      __ksTelemetryFieldTypeName: '@keystone-6/bigInt',
      views: '@keystone-6/core/fields/types/bigInt/views',
      getAdminMeta(): Parameters<typeof controller>[0]['fieldMeta'] {
        return {
          validation: {
            min: min?.toString() ?? `${MIN_INT}`,
            max: max?.toString() ?? `${MAX_INT}`,
          },
          defaultValue: typeof defaultValue === 'bigint' ? defaultValue.toString() : defaultValue,
        }
      },
    })
  }
}
</file>

<file path="packages/core/src/fields/types/bytes/views/index.tsx">
import { TextField } from '@keystar/ui/text-field'
import { useState } from 'react'

import type { TextFieldMeta } from '..'
import { NullableFieldWrapper } from '../../../../admin-ui/components'
import { entriesTyped } from '../../../../lib/core/utils'
import type {
  FieldController,
  FieldControllerConfig,
  FieldProps,
  SimpleFieldTypeInfo,
} from '../../../../types'

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, forceValidation, onChange, value, isRequired } = props

  const [shouldShowErrors, setShouldShowErrors] = useState(false)
  const validationMessages = validate(value, props.isRequired, field.label)

  const isReadOnly = onChange == null
  const isNull = value.inner.kind === 'null'

  return (
    <NullableFieldWrapper
      isAllowed={field.isNullable}
      autoFocus={isNull && autoFocus}
      label={field.label}
      isReadOnly={isReadOnly}
      isNull={isNull}
      onChange={() => {
        if (!onChange) return

        const inner =
          value.inner.kind === 'value'
            ? ({ kind: 'null', prev: value.inner.value } as const)
            : ({ kind: 'value', value: value.inner.prev } as const)

        onChange({ ...value, inner })
      }}
    >
      <TextField
        autoFocus={autoFocus}
        description={field.description}
        label={field.label}
        errorMessage={
          !!validationMessages.length && (shouldShowErrors || forceValidation)
            ? validationMessages.join('. ')
            : undefined
        }
        isDisabled={isNull}
        isReadOnly={isReadOnly}
        isRequired={isRequired}
        onBlur={() => {
          setShouldShowErrors(true)
        }}
        onChange={textValue => {
          if (!onChange) return
          onChange({
            ...value,
            inner: {
              kind: 'value',
              value: textValue,
            },
          })
        }}
        // maintain the previous value when set to null in aid of continuity for
        // the user. it will be cleared when the item is saved
        value={value.inner.kind === 'value' ? value.inner.value : value.inner.prev}
      />
    </NullableFieldWrapper>
  )
}

type Config = FieldControllerConfig<TextFieldMeta>

function validate(value: TextValue, isRequired: boolean, fieldLabel: string): string[] {
  // if the value is the same as the initial for an update, we don't want to block saving
  // since we're not gonna send it anyway if it's the same
  // and going "fix this thing that is unrelated to the thing you're doing" is bad
  // and also bc it could be null bc of read access control
  if (
    value.kind === 'update' &&
    ((value.initial.kind === 'null' && value.inner.kind === 'null') ||
      (value.initial.kind === 'value' &&
        value.inner.kind === 'value' &&
        value.inner.value === value.initial.value))
  ) {
    return []
  }

  if (value.inner.kind === 'null') {
    if (isRequired) return [`${fieldLabel} is required`]
    return []
  }
  return []
}

type InnerTextValue = { kind: 'null'; prev: string } | { kind: 'value'; value: string }
type TextValue =
  | { kind: 'create'; inner: InnerTextValue }
  | { kind: 'update'; inner: InnerTextValue; initial: InnerTextValue }

function deserializeTextValue(value: string | null): InnerTextValue {
  if (value === null) return { kind: 'null', prev: '' }
  return { kind: 'value', value }
}

export function controller(config: Config): FieldController<
  TextValue,
  string,
  SimpleFieldTypeInfo<'String'>['inputs']['where']
> & {
  isNullable: boolean
} {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    defaultValue: { kind: 'create', inner: deserializeTextValue(config.fieldMeta.defaultValue) },
    isNullable: config.fieldMeta.isNullable,
    deserialize: data => {
      const inner = deserializeTextValue(data[config.path])
      return { kind: 'update', inner, initial: inner }
    },
    serialize: value => ({ [config.path]: value.inner.kind === 'null' ? null : value.inner.value }),

    validate: (val, opts) => validate(val, opts.isRequired, config.label).length === 0,
    filter: {
      Filter(props) {
        const { autoFocus, context, typeLabel, onChange, type, value, ...otherProps } = props

        const labelProps =
          context === 'add' ? { label: config.label, description: typeLabel } : { label: typeLabel }

        // NOTE: "type" is a valid attribute for an input element, however the
        // prop represents a filter type in this context e.g. "contains_i", so
        // we're filtering it out of the spread.
        // TODO: more consideration is needed for the filter API, once
        // requirements are better understood.
        return (
          <TextField
            {...otherProps}
            {...labelProps}
            autoFocus={autoFocus}
            onChange={onChange}
            value={value}
          />
        )
      },
      Label({ label, value }) {
        const trimmedLabel = label.toLowerCase().replace(' exactly', '')
        return `${trimmedLabel} "${value}"`
      },

      graphql: ({ type, value }) => {
        const isNot = type.startsWith('not_')
        const filter = { equals: value }
        return {
          [config.path]: {
            ...(isNot ? { not: filter } : filter),
          },
        }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, _value]) => {
          if (!_value) return []
          if (type === 'equals') return { type: 'is_i', value: _value as string }
          if (type === 'not') {
            const notValue = _value as any
            if (notValue?.equals) return { type: 'not_i', value: notValue.equals as string }
          }
          return []
        })
      },
      types: {
        is_i: {
          label: 'Is exactly',
          initialValue: '',
        },
        not_i: {
          label: 'Is not exactly',
          initialValue: '',
        },
      },
    },
  }
}
</file>

<file path="packages/core/src/fields/types/bytes/index.ts">
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import { weakMemoize } from '../../../lib/core/utils'
import { GraphQLError } from 'graphql'
import type {
  GArg,
  GInputObjectType,
  GList,
  GNonNull,
  GScalarType,
  InferValueFromInputType,
} from '@graphql-ts/schema'

export type FieldTypeInfo = {
  item: Uint8Array | null
  inputs: {
    create: Uint8Array | null | undefined
    update: Uint8Array | null | undefined
    where: InferValueFromInputType<BytesFilterType> | undefined
    uniqueWhere: Uint8Array | null | undefined
    orderBy: undefined
  }
  prisma: {
    create: Uint8Array | null | undefined
    update: Uint8Array | null | undefined
  }
}

export type BytesFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> & {
  isIndexed?: boolean | 'unique'
  graphql?: {
    scalar?: GScalarType<Uint8Array, string>
  }
  validation?: {
    /**
     * Makes the field disallow null values. It does not constrain the length of the value.
     */
    isRequired?: boolean
    /**
     * Specifies the minimum and maximum length of the value in _bytes_. It does _not_ represent the length in the encoded form.
     */
    length?: { min?: number; max?: number }
  }
  defaultValue?: Uint8Array | null
  db?: {
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
    /**
     * The underlying database type.
     * Only some of the types are supported on PostgreSQL and MySQL.
     * The native type is not customisable on SQLite.
     * See Prisma's documentation for more information about the supported types.
     *
     * https://www.prisma.io/docs/orm/reference/prisma-schema-reference#bytes
     */
    nativeType?: string
  }
}

export type TextFieldMeta = {
  isNullable: boolean
  defaultValue: string | null
}

export function bytes<ListTypeInfo extends BaseListTypeInfo>(
  config: BytesFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { defaultValue = null, isIndexed, validation = {} } = config

  const scalar = config.graphql?.scalar ?? g.Hex

  config.db ??= {}

  const isRequired = validation.isRequired ?? false
  const min = validation.isRequired ? (validation.length?.min ?? 1) : validation.length?.min
  const max = validation.length?.max

  return meta => {
    {
      const serializedExample = scalar.serialize(new Uint8Array([0]))
      if (typeof serializedExample !== 'string') {
        throw new Error(
          `The GraphQL scalar type specified for ${meta.listKey}.${meta.fieldKey} must serialize Uint8Arrays to strings`
        )
      }
    }
    if (min !== undefined && (!Number.isInteger(min) || min < 0)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.length.min: ${min} but it must be a positive integer`
      )
    }
    if (max !== undefined && (!Number.isInteger(max) || max < 0)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.length.max: ${max} but it must be a positive integer`
      )
    }
    if (min !== undefined && max !== undefined && min > max) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a validation.length.max that is less than the validation.length.min, and therefore has no valid options`
      )
    }

    const hasAdditionalValidation = min !== undefined || max !== undefined
    const { mode, validate } = makeValidateHook(
      meta,
      config,
      hasAdditionalValidation
        ? ({ resolvedData, operation, addValidationError }) => {
            if (operation === 'delete') return

            const value: Uint8Array | null | undefined = resolvedData[meta.fieldKey]
            if (value != null) {
              if (min !== undefined && value.length < min) {
                if (min === 1) {
                  addValidationError(`value must not be empty`)
                } else {
                  addValidationError(`value must be at least ${min} bytes long`)
                }
              }
              if (max !== undefined && value.length > max) {
                addValidationError(`value must be no longer than ${max} bytes`)
              }
            }
          }
        : undefined
    )

    let clientSideDefaultValue = null
    if (defaultValue !== null) {
      clientSideDefaultValue = scalar.serialize(defaultValue)
      if (typeof clientSideDefaultValue !== 'string') {
        throw new Error(
          `The GraphQL scalar type specified for ${meta.listKey}.${meta.fieldKey} must serialize Uint8Arrays to strings`
        )
      }
    } else if (mode === 'required') {
      clientSideDefaultValue = scalar.serialize(new Uint8Array(0))
      if (typeof clientSideDefaultValue !== 'string') {
        throw new Error(
          `The GraphQL scalar type specified for ${meta.listKey}.${meta.fieldKey} must serialize Uint8Arrays to strings`
        )
      }
    }

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: 'Bytes',
      default: defaultValue === null ? undefined : { kind: 'literal', value: defaultValue },
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      map: config.db?.map,
      nativeType: config.db?.nativeType,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, isRequired),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: scalar }) } : undefined,
        where: {
          arg: g.arg({
            type: mode === 'optional' ? getNullableFilterType(scalar) : getFilterType(scalar),
          }),
        },
        create: {
          arg: g.arg({
            type: scalar,
          }),
          resolve(val) {
            if (val !== undefined) return val
            return defaultValue ?? null
          },
        },
        update: { arg: g.arg({ type: scalar }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({ type: scalar }),
      __ksTelemetryFieldTypeName: '@keystone-6/bytes',
      views: '@keystone-6/core/fields/types/bytes/views',
      getAdminMeta(): TextFieldMeta {
        return {
          defaultValue: clientSideDefaultValue,
          isNullable: mode === 'optional',
        }
      },
    })
  }
}

type BytesFilterType = GInputObjectType<{
  equals?: GArg<GScalarType<Uint8Array, string>>
  in?: GArg<GList<GNonNull<GScalarType<Uint8Array, string>>>>
  notIn?: GArg<GList<GNonNull<GScalarType<Uint8Array, string>>>>
  not?: GArg<BytesFilterType>
}>

// the weakMemoizes are important so reusing the same scalar type for multiple `bytes` fields uses the same (===) filter type
// rather than a duplicate one which would cause an error about two types with the same name
const getFilterType = weakMemoize((scalar: GScalarType<Uint8Array, string>) => {
  const filter: BytesFilterType = g.inputObject({
    name: `${scalar.name}Filter`,
    fields: () => ({
      equals: g.arg({ type: scalar }),
      in: g.arg({ type: g.list(g.nonNull(scalar)) }),
      notIn: g.arg({ type: g.list(g.nonNull(scalar)) }),
      not: g.arg({ type: filter }),
    }),
  })
  return filter
})

const getNullableFilterType = weakMemoize((scalar: GScalarType<Uint8Array, string>) => {
  const filter: BytesFilterType = g.inputObject({
    name: `${scalar.name}NullableFilter`,
    fields: () => ({
      equals: g.arg({ type: scalar }),
      in: g.arg({ type: g.list(g.nonNull(scalar)) }),
      notIn: g.arg({ type: g.list(g.nonNull(scalar)) }),
      not: g.arg({ type: filter }),
    }),
  })
  return filter
})

export function bytesScalar(opts: {
  name: string
  serialize: (value: Uint8Array) => string
  parse: (value: string) => Uint8Array
  description?: string
  specifiedByURL?: string
}) {
  return g.scalar<Uint8Array, string>({
    name: opts.name,
    description: opts.description,
    specifiedByURL: opts.specifiedByURL,
    parseLiteral(value) {
      if (value.kind !== 'StringValue') {
        throw new GraphQLError(`${opts.name} only accepts values as strings`)
      }
      return opts.parse(value.value)
    },
    parseValue(value) {
      // so that when you're doing a mutation in a resolver, you can just pass in a Uint8Array directly
      if (value instanceof Uint8Array) {
        // duplicate it though to avoid any weirdness with the array being mutated
        // + ensuring that if you pass in a Buffer, resolvers recieve a normal Uint8Array
        return Uint8Array.from(value)
      }
      if (typeof value !== 'string') {
        throw new GraphQLError(`${opts.name} only accepts values as strings`)
      }
      return opts.parse(value)
    },
    serialize(value) {
      if (!(value instanceof Uint8Array)) {
        throw new GraphQLError(`unexpected value provided to ${opts.name} scalar: ${value}`)
      }
      return opts.serialize(value)
    },
  })
}
</file>

<file path="packages/core/src/fields/types/calendarDay/views/index.tsx">
import { CalendarDate, getLocalTimeZone, now, parseDate } from '@internationalized/date'
import { useDateFormatter } from '@react-aria/i18n'
import { useMemo, useReducer, useState } from 'react'

import { ToggleButton } from '@keystar/ui/button'
import { DatePicker } from '@keystar/ui/date-time'
import { Icon } from '@keystar/ui/icon'
import { calendarClockIcon } from '@keystar/ui/icon/icons/calendarClockIcon'
import { Grid } from '@keystar/ui/layout'
import { TextField } from '@keystar/ui/text-field'
import { TooltipTrigger, Tooltip } from '@keystar/ui/tooltip'
import { Text } from '@keystar/ui/typography'

import {
  type CellComponent,
  type FieldController,
  type FieldControllerConfig,
  type FieldProps,
} from '../../../../types'

export type Value =
  | { kind: 'create'; value: string | null }
  | { kind: 'update'; value: string | null; initial: string | null }

export function Field(props: FieldProps<typeof controller>) {
  const { field, value, forceValidation, onChange, isRequired } = props
  const parsedValue = value.value ? parseDate(value.value) : null

  const [isDirty, setDirty] = useState(false)
  const [isReadonlyUTC, toggleReadonlyUTC] = useReducer(prev => !prev, false)
  const dateFormatter = useDateFormatter({ dateStyle: 'long' })
  const placeholderValue = useMemo(() => {
    const today = now(getLocalTimeZone())
    return new CalendarDate(today.year, today.month, today.day)
  }, [])

  // the read-only date field is deceptively interactive, better to render a
  // text field to avoid confusion. when there's no value the field is disabled,
  // placeholder text is shown, and the toggle button is hidden
  if (!onChange) {
    return (
      <Grid
        columns={parsedValue ? 'minmax(0, 1fr) auto' : undefined}
        gap="regular"
        alignItems="end"
      >
        <TextField
          label={field.label}
          description={field.description}
          isDisabled={!parsedValue}
          isReadOnly
          value={
            parsedValue
              ? isReadonlyUTC
                ? parsedValue.toString()
                : dateFormatter.format(parsedValue.toDate(getLocalTimeZone()))
              : 'yyyy-mm-dd'
          }
        />
        {!!parsedValue && (
          <TooltipTrigger>
            <ToggleButton
              aria-label="utc time"
              isSelected={isReadonlyUTC}
              onPress={toggleReadonlyUTC}
            >
              <Icon src={calendarClockIcon} />
            </ToggleButton>
            <Tooltip>Local / UTC</Tooltip>
          </TooltipTrigger>
        )}
      </Grid>
    )
  }

  const showValidation = isDirty || forceValidation
  const validationMessage = showValidation ? validate(value, isRequired, field.label) : undefined

  return (
    <DatePicker
      label={field.label}
      description={field.description}
      errorMessage={showValidation ? validationMessage : undefined}
      granularity="day"
      // isReadOnly={undefined} // read-only state handled above
      isRequired={isRequired}
      // NOTE: in addition to providing a cue for users about the expected input
      // format, the `placeholderValue` determines the type of value for the
      // field. the implementation below ensures `CalendarDate` so we can avoid
      // unnecessary guards or transformations.
      placeholderValue={placeholderValue}
      onBlur={() => setDirty(true)}
      onChange={datetime => {
        onChange({ ...value, value: datetime?.toString() ?? null })
      }}
      value={parsedValue}
    />
  )
}

function validate(value: Value, isRequired: boolean, label: string): string | undefined {
  // if we recieve null initially on the item view and the current value is null,
  // we should always allow saving it because:
  // - the value might be null in the database and we don't want to prevent saving the whole item because of that
  // - we might have null because of an access control error
  if (value.kind === 'update' && value.initial === null && value.value === null) {
    return undefined
  }

  if (isRequired && value.value === null) {
    return `${label} is required`
  }
  return undefined
}

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  const dateFormatter = useDateFormatter({ dateStyle: 'medium' })
  return value ? <Text>{dateFormatter.format(new Date(value))}</Text> : null
}

export type CalendarDayFieldMeta = {
  defaultValue: string | null
}

export function controller(
  config: FieldControllerConfig<CalendarDayFieldMeta>
): FieldController<Value, string> & { fieldMeta: CalendarDayFieldMeta } {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    fieldMeta: config.fieldMeta,
    defaultValue: { kind: 'create', value: null },
    deserialize: data => {
      const value = data[config.path]
      return { kind: 'update', initial: value, value }
    },
    serialize: ({ value }) => {
      return { [config.path]: value }
    },
    validate: (value, opts) => validate(value, opts.isRequired, config.label) === undefined,
  }
}
</file>

<file path="packages/core/src/fields/types/calendarDay/index.ts">
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { type CalendarDayFieldMeta } from './views'
import { g } from '../../..'
import { filters } from '../../filters'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import type {
  GInputObjectType,
  GArg,
  GList,
  GNonNull,
  InferValueFromInputType,
} from '@graphql-ts/schema'

export type CalendarDayFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'String' | 'DateTime'>
> & {
  isIndexed?: boolean | 'unique'
  validation?: {
    isRequired?: boolean
  }
  defaultValue?: string
  db?: {
    isNullable?: boolean
    extendPrismaSchema?: (field: string) => string
    map?: string
  }
}

export function calendarDay<ListTypeInfo extends BaseListTypeInfo>(
  config: CalendarDayFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { isIndexed, validation, defaultValue } = config
  return meta => {
    if (typeof defaultValue === 'string') {
      try {
        g.CalendarDay.parseValue(defaultValue)
      } catch (err) {
        throw new Error(
          `The calendarDay field at ${meta.listKey}.${meta.fieldKey} specifies defaultValue: ${defaultValue} but values must be provided as a full-date ISO8601 string such as 1970-01-01`
        )
      }
    }

    const usesNativeDateType = meta.provider === 'postgresql' || meta.provider === 'mysql'

    function resolveInput(value: string | null | undefined) {
      if (meta.provider === 'sqlite' || value == null) {
        return value
      }
      return dateStringToDateObjectInUTC(value)
    }

    const { mode, validate } = makeValidateHook(meta, config)
    const commonResolveFilter = mode === 'optional' ? filters.resolveCommon : <T>(x: T) => x

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: usesNativeDateType ? 'DateTime' : 'String',
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      default:
        typeof defaultValue === 'string'
          ? {
              kind: 'literal',
              value: defaultValue,
            }
          : undefined,
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
      nativeType: usesNativeDateType ? 'Date' : undefined,
    })({
      ...config,
      ...defaultIsRequired(config, validation?.isRequired ?? false),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere:
          isIndexed === 'unique'
            ? {
                arg: g.arg({ type: g.CalendarDay }),
                resolve: usesNativeDateType ? dateStringToDateObjectInUTC : undefined,
              }
            : undefined,
        where: {
          arg: g.arg({
            type: mode === 'optional' ? CalendarDayNullableFilter : CalendarDayFilter,
          }),
          resolve: usesNativeDateType
            ? value => commonResolveFilter(transformFilterDateStringsToDateObjects(value))
            : commonResolveFilter,
        },
        create: {
          arg: g.arg({
            type: g.CalendarDay,
            defaultValue,
          }),
          resolve(val: string | null | undefined) {
            if (val === undefined) {
              val = defaultValue ?? null
            }
            return resolveInput(val)
          },
        },
        update: { arg: g.arg({ type: g.CalendarDay }), resolve: resolveInput },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({
        type: g.CalendarDay,
        resolve({ value }) {
          if (value instanceof Date) {
            return value.toISOString().slice(0, 10)
          }
          return value
        },
      }),
      __ksTelemetryFieldTypeName: '@keystone-6/calendarDay',
      views: '@keystone-6/core/fields/types/calendarDay/views',
      getAdminMeta(): CalendarDayFieldMeta {
        return {
          defaultValue: defaultValue ?? null,
        }
      },
    })
  }
}

function dateStringToDateObjectInUTC(value: string) {
  return new Date(`${value}T00:00Z`)
}

type CalendarDayFilterType = GInputObjectType<{
  equals: GArg<typeof g.CalendarDay>
  in: GArg<GList<GNonNull<typeof g.CalendarDay>>>
  notIn: GArg<GList<GNonNull<typeof g.CalendarDay>>>
  lt: GArg<typeof g.CalendarDay>
  lte: GArg<typeof g.CalendarDay>
  gt: GArg<typeof g.CalendarDay>
  gte: GArg<typeof g.CalendarDay>
  not: GArg<CalendarDayFilterType>
}>

function transformFilterDateStringsToDateObjects(
  filter: InferValueFromInputType<CalendarDayFilterType>
): Parameters<typeof filters.resolveCommon>[0] {
  if (filter === null) {
    return filter
  }
  return Object.fromEntries(
    Object.entries(filter).map(([key, value]) => {
      if (value == null) {
        return [key, value]
      }
      if (Array.isArray(value)) {
        return [key, value.map(dateStringToDateObjectInUTC)]
      }
      if (typeof value === 'object') {
        return [key, transformFilterDateStringsToDateObjects(value)]
      }
      return [key, dateStringToDateObjectInUTC(value)]
    })
  )
}

const filterFields = (nestedType: CalendarDayFilterType) => ({
  equals: g.arg({ type: g.CalendarDay }),
  in: g.arg({ type: g.list(g.nonNull(g.CalendarDay)) }),
  notIn: g.arg({ type: g.list(g.nonNull(g.CalendarDay)) }),
  lt: g.arg({ type: g.CalendarDay }),
  lte: g.arg({ type: g.CalendarDay }),
  gt: g.arg({ type: g.CalendarDay }),
  gte: g.arg({ type: g.CalendarDay }),
  not: g.arg({ type: nestedType }),
})

const CalendarDayNullableFilter: CalendarDayFilterType = g.inputObject({
  name: 'CalendarDayNullableFilter',
  fields: () => filterFields(CalendarDayNullableFilter),
})

const CalendarDayFilter: CalendarDayFilterType = g.inputObject({
  name: 'CalendarDayFilter',
  fields: () => filterFields(CalendarDayFilter),
})
</file>

<file path="packages/core/src/fields/types/checkbox/views/index.tsx">
import { Checkbox } from '@keystar/ui/checkbox'
import { Icon } from '@keystar/ui/icon'
import { checkIcon } from '@keystar/ui/icon/icons/checkIcon'
import { Text, VisuallyHidden } from '@keystar/ui/typography'

import type {
  CellComponent,
  FieldController,
  FieldControllerConfig,
  FieldProps,
  SimpleFieldTypeInfo,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'

export function Field({ field, value, onChange, autoFocus }: FieldProps<typeof controller>) {
  return (
    <Checkbox
      autoFocus={autoFocus}
      isReadOnly={onChange == null}
      isSelected={value}
      onChange={onChange}
    >
      <Text>{field.label}</Text>
      {field.description && <Text slot="description">{field.description}</Text>}
    </Checkbox>
  )
}

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  return value ? <Icon src={checkIcon} aria-label="true" /> : <VisuallyHidden>false</VisuallyHidden>
}

type CheckboxController = FieldController<
  boolean,
  boolean,
  SimpleFieldTypeInfo<'Boolean'>['inputs']['where']
>

export function controller(
  config: FieldControllerConfig<{ defaultValue: boolean }>
): CheckboxController {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    defaultValue: config.fieldMeta.defaultValue,
    deserialize(item) {
      const value = item[config.path]
      return typeof value === 'boolean' ? value : false
    },
    serialize(value) {
      return { [config.path]: value }
    },
    filter: {
      Filter(props) {
        const { autoFocus, context, typeLabel, onChange, value, type, ...otherProps } = props
        return (
          <Checkbox autoFocus={autoFocus} onChange={onChange} isSelected={value} {...otherProps}>
            {typeLabel} {config.label.toLocaleLowerCase()}
          </Checkbox>
        )
      },
      Label({ label, type, value }) {
        return `${label.toLowerCase()} ${value ? 'true' : 'false'}`
      },
      graphql({ type, value }) {
        return {
          [config.path]: {
            equals: type === 'not' ? !value : value,
          },
        }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (value == null) return []
          if (type === 'equals') return { type: 'is', value }
          if (type === 'not') {
            if (value.equals == null) return []
            return { type: 'not', value: value.equals }
          }
          return []
        })
      },
      types: {
        is: {
          label: 'Is',
          initialValue: true,
        },
        not: {
          label: 'Is not',
          initialValue: true,
        },
      },
    },
  }
}
</file>

<file path="packages/core/src/fields/types/checkbox/index.ts">
import { userInputError } from '../../../lib/core/graphql-errors'
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { assertReadIsNonNullAllowed } from '../../non-null-graphql'
import { filters } from '../../filters'
import type { controller } from './views'

export type CheckboxFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'Boolean'>
> & {
  defaultValue?: boolean
  db?: {
    map?: string
    extendPrismaSchema?: (field: string) => string
  }
}

export function checkbox<ListTypeInfo extends BaseListTypeInfo>(
  config: CheckboxFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { defaultValue = false } = config

  return meta => {
    if ((config as any).isIndexed === 'unique') {
      throw TypeError("isIndexed: 'unique' is not a supported option for field type checkbox")
    }

    assertReadIsNonNullAllowed(meta, config, false)

    return fieldType({
      kind: 'scalar',
      mode: 'required',
      scalar: 'Boolean',
      default: { kind: 'literal', value: defaultValue },
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      input: {
        where: { arg: g.arg({ type: filters[meta.provider].Boolean.required }) },
        create: {
          arg: g.arg({
            type: g.Boolean,
            defaultValue: typeof defaultValue === 'boolean' ? defaultValue : undefined,
          }),
          resolve(val) {
            if (val === null) throw userInputError('Checkbox fields cannot be set to null')
            return val ?? defaultValue
          },
        },
        update: {
          arg: g.arg({ type: g.Boolean }),
          resolve(val) {
            if (val === null) throw userInputError('Checkbox fields cannot be set to null')
            return val
          },
        },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({ type: g.Boolean }),
      __ksTelemetryFieldTypeName: '@keystone-6/checkbox',
      views: '@keystone-6/core/fields/types/checkbox/views',
      getAdminMeta: (): Parameters<typeof controller>[0]['fieldMeta'] => ({
        defaultValue,
      }),
    })
  }
}
</file>

<file path="packages/core/src/fields/types/decimal/views/index.tsx">
import { useState } from 'react'

import { TextField } from '@keystar/ui/text-field'

import {
  type FieldController,
  type FieldControllerConfig,
  type FieldProps,
  type SimpleFieldTypeInfo,
  Decimal,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'

const TYPE_OPERATOR_MAP = {
  equals: '=',
  not: '≠',
  gt: '>',
  lt: '<',
  gte: '≥',
  lte: '≤',
} as const

type Value =
  | { kind: 'create'; value: string | null }
  | { kind: 'update'; initial: string | null; value: string | null }

type Validation = {
  min: string | null
  max: string | null
}

function validate_(
  value: Value,
  validation: Validation,
  isRequired: boolean,
  label: string
): string | undefined {
  const { value: input, kind } = value
  if (kind === 'update' && value.initial === null && input === null) return
  if (isRequired && input === null) return `${label} is required`
  if (typeof input !== 'string') return
  try {
    const v = new Decimal(input)
    if (!v.isFinite()) return `${label} is not finite`
    if (validation.min && v.lessThan(new Decimal(validation.min)))
      return `${label} must be greater than or equal to ${validation.min}`
    if (validation.max && v.greaterThan(new Decimal(validation.max)))
      return `${label} must be less than or equal to ${validation.max}`
  } catch (e: any) {
    return e?.message ?? 'error'
  }
}

export function controller(
  config: FieldControllerConfig<{
    validation: Validation
    defaultValue: string | null
  }>
): FieldController<Value, string | null, SimpleFieldTypeInfo<'Decimal'>['inputs']['where']> & {
  validation: Validation
} {
  const validate = (value: Value, opts: { isRequired: boolean }) => {
    return validate_(value, config.fieldMeta.validation, opts.isRequired, config.label)
  }

  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    validation: config.fieldMeta.validation,
    defaultValue: { kind: 'create', value: config.fieldMeta.defaultValue },
    deserialize: data => ({ kind: 'update', value: data[config.path], initial: data[config.path] }),
    serialize: value => ({ [config.path]: value.value }),
    filter: {
      Filter(props) {
        const {
          autoFocus,
          context,
          forceValidation,
          typeLabel,
          onChange,
          type,
          value,
          ...otherProps
        } = props
        const [isDirty, setDirty] = useState(false)
        if (type === 'empty' || type === 'not_empty') return null

        const labelProps =
          context === 'add' ? { label: config.label, description: typeLabel } : { label: typeLabel }

        return (
          <TextField
            {...otherProps}
            {...labelProps}
            autoFocus={autoFocus}
            errorMessage={
              (forceValidation || isDirty) &&
              !validate({ kind: 'update', initial: null, value }, { isRequired: true })
                ? 'Required'
                : null
            }
            inputMode="decimal"
            width="auto"
            onBlur={() => setDirty(true)}
            onChange={x => onChange?.(x === '' ? null : x)}
            value={value ?? ''}
          />
        )
      },

      graphql: ({ type, value }) => {
        if (type === 'empty') return { [config.path]: { equals: null } }
        if (type === 'not_empty') return { [config.path]: { not: { equals: null } } }
        if (type === 'not') return { [config.path]: { not: { equals: value } } }
        return { [config.path]: { [type]: value } }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (type === 'equals' && value === null) {
            return [{ type: 'empty', value: null }]
          }
          if (!value) return []
          if (type === 'equals') return { type: 'equals', value: value as unknown as string }
          if (type === 'not') {
            if (value.equals === null) return { type: 'not_empty', value: null }
            return { type: 'not', value: value.equals as unknown as string }
          }
          if (type === 'gt' || type === 'gte' || type === 'lt' || type === 'lte') {
            return { type, value: value as unknown as string }
          }
          return []
        })
      },
      Label({ label, type, value }) {
        if (type === 'empty' || type === 'not_empty') return label.toLocaleLowerCase()
        const operator = TYPE_OPERATOR_MAP[type as keyof typeof TYPE_OPERATOR_MAP]
        return `${operator} ${value}`
      },
      types: {
        equals: {
          label: 'Is exactly',
          initialValue: null,
        },
        not: {
          label: 'Is not exactly',
          initialValue: null,
        },
        gt: {
          label: 'Is greater than',
          initialValue: null,
        },
        lt: {
          label: 'Is less than',
          initialValue: null,
        },
        gte: {
          label: 'Is greater than or equal to',
          initialValue: null,
        },
        lte: {
          label: 'Is less than or equal to',
          initialValue: null,
        },
        empty: {
          label: 'Is empty',
          initialValue: null,
        },
        not_empty: {
          label: 'Is not empty',
          initialValue: null,
        },
      },
    },

    validate: (value, opts) => validate(value, opts) === undefined,
  }
}

export function Field({
  field,
  value,
  onChange,
  autoFocus,
  forceValidation,
  isRequired,
}: FieldProps<typeof controller>) {
  const [isDirty, setDirty] = useState(false)
  const isReadOnly = !onChange

  const validate = (value: Value) => {
    return validate_(value, field.validation, isRequired, field.label)
  }

  return (
    <TextField
      autoFocus={autoFocus}
      description={field.description}
      label={field.label}
      errorMessage={(forceValidation || isDirty) && validate(value)}
      isReadOnly={isReadOnly}
      isRequired={isRequired}
      inputMode="numeric"
      width="alias.singleLineWidth"
      onBlur={() => setDirty(true)}
      onChange={x => onChange?.({ ...value, value: x === '' ? null : x })}
      value={value.value ?? ''}
    />
  )
}
</file>

<file path="packages/core/src/fields/types/decimal/index.ts">
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
  Decimal,
} from '../../../types'
import { g } from '../../..'
import { filters } from '../../filters'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import type { controller } from './views'

export type DecimalFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'Decimal'>
> & {
  isIndexed?: boolean | 'unique'
  defaultValue?: string | null
  validation?: {
    isRequired?: boolean
    min?: string
    max?: string
  }
  precision?: number
  scale?: number
  db?: {
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
  }
}

function safeParseDecimal(value: string | null | undefined) {
  if (value === null || value === undefined) return value
  const result = new Decimal(value)
  if (!result.isFinite()) throw new Error(`"${value}" is not finite`)
  return result
}

// TODO: https://github.com/Thinkmill/keystatic/blob/main/design-system/pkg/src/number-field/NumberField.tsx
export function decimal<ListTypeInfo extends BaseListTypeInfo>(
  config: DecimalFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const {
    defaultValue: defaultValue_,
    isIndexed,
    precision = 18,
    scale = 4,
    validation = {},
  } = config

  const { isRequired = false, min, max } = validation
  const defaultValue = typeof defaultValue_ === 'string' ? defaultValue_ : null

  const parsedDefaultValue = safeParseDecimal(defaultValue)
  const parsedMax = safeParseDecimal(max) ?? undefined
  const parsedMin = safeParseDecimal(min) ?? undefined

  return meta => {
    if (meta.provider === 'sqlite') {
      throw new Error('The decimal field does not support sqlite')
    }
    if (!Number.isInteger(scale)) {
      throw new TypeError(
        `The scale for decimal fields must be an integer but the scale for the decimal field at ${meta.listKey}.${meta.fieldKey} is not an integer`
      )
    }
    if (!Number.isInteger(precision)) {
      throw new TypeError(
        `The precision for decimal fields must be an integer but the precision for the decimal field at ${meta.listKey}.${meta.fieldKey} is not an integer`
      )
    }
    if (scale > precision) {
      throw new Error(
        `The scale configured for decimal field at ${meta.listKey}.${meta.fieldKey} (${scale}) ` +
          `must not be larger than the field's precision (${precision})`
      )
    }
    if (defaultValue !== null && !parsedDefaultValue) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a default value of: ${defaultValue} but it must be a valid finite number`
      )
    }
    if (min !== undefined && !parsedMin) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.min: ${min} but it must be a valid finite number`
      )
    }
    if (max !== undefined && !parsedMax) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.max: ${max} but it must be a valid finite number`
      )
    }
    if (min !== undefined && max !== undefined && parsedMin && parsedMax && parsedMin > parsedMax) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a validation.max that is less than the validation.min, and therefore has no valid options`
      )
    }

    const hasAdditionalValidation = min !== undefined || max !== undefined
    const { mode, validate } = makeValidateHook(
      meta,
      config,
      hasAdditionalValidation
        ? ({ resolvedData, operation, addValidationError }) => {
            if (operation === 'delete') return

            const value = safeParseDecimal(resolvedData[meta.fieldKey])
            if (value != null) {
              if (parsedMin !== undefined && value.lessThan(parsedMin)) {
                addValidationError(`value must be greater than or equal to ${min}`)
              }

              if (parsedMax !== undefined && value.greaterThan(parsedMax)) {
                addValidationError(`value must be less than or equal to ${max}`)
              }
            }
          }
        : undefined
    )

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: 'Decimal',
      nativeType: `Decimal(${precision}, ${scale})`,
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      default:
        typeof defaultValue === 'string' ? { kind: 'literal', value: defaultValue } : undefined,
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, isRequired),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.Decimal }) } : undefined,
        where: {
          arg: g.arg({ type: filters[meta.provider].Decimal[mode] }),
          resolve: mode === 'optional' ? filters.resolveCommon : undefined,
        },
        create: {
          arg: g.arg({
            type: g.Decimal,
            defaultValue: parsedDefaultValue,
          }),
          resolve(val) {
            if (val === undefined) return parsedDefaultValue
            return val
          },
        },
        update: { arg: g.arg({ type: g.Decimal }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({
        type: g.Decimal,
        resolve({ value }) {
          if (value === null) return null
          const val: Decimal & { scaleToPrint?: number } = new Decimal(value)
          val.scaleToPrint = scale
          return val
        },
      }),
      __ksTelemetryFieldTypeName: '@keystone-6/decimal',
      views: '@keystone-6/core/fields/types/decimal/views',
      getAdminMeta(): Parameters<typeof controller>[0]['fieldMeta'] {
        return {
          validation: {
            max: max ?? null,
            min: min ?? null,
          },
          defaultValue,
        }
      },
    })
  }
}
</file>

<file path="packages/core/src/fields/types/file/views/Field.tsx">
import { useLocale } from '@react-aria/i18n'
import bytes, { type BytesOptions } from 'bytes'
import { extname } from 'path'
import {
  type PropsWithChildren,
  type ReactElement,
  type SyntheticEvent,
  Fragment,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

import { ActionButton } from '@keystar/ui/button'
import { Icon } from '@keystar/ui/icon'
import { fileIcon } from '@keystar/ui/icon/icons/fileIcon'
import { fileAudioIcon } from '@keystar/ui/icon/icons/fileAudioIcon'
import { fileCodeIcon } from '@keystar/ui/icon/icons/fileCodeIcon'
import { fileDigitIcon } from '@keystar/ui/icon/icons/fileDigitIcon'
import { fileImageIcon } from '@keystar/ui/icon/icons/fileImageIcon'
import { fileJsonIcon } from '@keystar/ui/icon/icons/fileJsonIcon'
import { fileSpreadsheetIcon } from '@keystar/ui/icon/icons/fileSpreadsheetIcon'
import { fileTextIcon } from '@keystar/ui/icon/icons/fileTextIcon'
import { fileVideoIcon } from '@keystar/ui/icon/icons/fileVideoIcon'
import { fileUpIcon } from '@keystar/ui/icon/icons/fileUpIcon'
import { Field as KeystarField } from '@keystar/ui/field'
import { HStack, VStack } from '@keystar/ui/layout'
import { css } from '@keystar/ui/style'
import { Text } from '@keystar/ui/typography'

import type { FieldProps } from '../../../../types'
import type { FileValue } from './index'
import type { controller } from '.'

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, onChange, value } = props

  const inputRef = useRef<HTMLInputElement | null>(null)
  const errorMessage = createErrorMessage(value)

  const onUploadChange = ({
    currentTarget: { validity, files },
  }: SyntheticEvent<HTMLInputElement>) => {
    const file = files?.[0]
    if (!file) return // bail if the user cancels from the file browser
    onChange?.({
      kind: 'upload',
      data: { file, validity },
      previous: value,
    })
  }
  const onFileTrigger = () => {
    inputRef.current?.click()
  }

  // Generate a random input key when the value changes, to ensure the file input is unmounted and
  // remounted (this is the only way to reset its value and ensure onChange will fire again if
  // the user selects the same file again)
  const inputKey = useMemo(() => Math.random(), [value])

  return (
    <KeystarField label={field.label} description={field.description} errorMessage={errorMessage}>
      {inputProps => (
        <Fragment>
          <FileView
            isInvalid={Boolean(errorMessage)}
            onFileTrigger={onFileTrigger}
            onChange={onChange}
            value={value}
          />
          <input
            {...inputProps}
            // TODO: add support for configurable file types
            // @see https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/accept
            autoComplete="off"
            autoFocus={autoFocus}
            disabled={onChange === undefined}
            hidden
            key={inputKey}
            name={field.path}
            onChange={onUploadChange}
            ref={inputRef}
            type="file"
          />
        </Fragment>
      )}
    </KeystarField>
  )
}

function FileView(props: {
  onFileTrigger: () => void
  isInvalid?: boolean
  onChange?: (value: FileValue) => void
  value: FileValue
}) {
  const { isInvalid, onFileTrigger, onChange, value } = props
  const fileData = useFileData(value)

  return (
    <VStack gap="medium">
      {isInvalid || !fileData ? null : (
        <FileDetails {...fileData}>
          {onChange && (
            <HStack gap="regular" marginTop="auto">
              <ActionButton onPress={onFileTrigger}>Change</ActionButton>
              {value.kind === 'from-server' && (
                <ActionButton
                  prominence="low"
                  onPress={() => {
                    onChange({ kind: 'remove', previous: value })
                  }}
                >
                  Remove
                </ActionButton>
              )}
              {value.kind === 'upload' && (
                <ActionButton
                  prominence="low"
                  onPress={() => {
                    onChange(value.previous)
                  }}
                >
                  Cancel
                </ActionButton>
              )}
            </HStack>
          )}
        </FileDetails>
      )}

      {!fileData && (
        <HStack gap="regular">
          <ActionButton isDisabled={onChange === undefined} onPress={onFileTrigger}>
            Upload
          </ActionButton>
          {value.kind === 'remove' && value.previous && (
            <ActionButton
              prominence="low"
              onPress={() => {
                if (value.previous !== undefined) {
                  onChange?.(value?.previous)
                }
              }}
            >
              Undo removal
            </ActionButton>
          )}
        </HStack>
      )}
    </VStack>
  )
}

function FileDetails(props: PropsWithChildren<FileData>) {
  const trimStartStyles = useTrimStartStyles()

  return (
    <HStack
      backgroundColor="canvas"
      border="neutral"
      borderRadius="regular"
      gap="medium"
      padding="medium"
    >
      <a href={props.href} download={props.name}>
        <HStack
          alignItems="center"
          backgroundColor="surfaceTertiary"
          borderRadius="small"
          justifyContent="center"
          height="100%"
          UNSAFE_className={css({ aspectRatio: '1 / 1' })}
        >
          <Icon src={props.icon} size="medium" color="neutral" />
        </HStack>
      </a>
      <VStack gap="medium" minWidth="alias.singleLineWidth" flex>
        <Text>
          <span className={css(trimStartStyles)} title={props.name}>
            {props.name}
          </span>
        </Text>
        <Text size="small" color="neutralSecondary">
          {formatBytes(props.size)}
        </Text>

        {/* field controls dependant on value type */}
        {props.children}
      </VStack>
    </HStack>
  )
}

export function formatBytes(size: number, options = defaultBytesOptions(size)) {
  return bytes(size, options)
}

function defaultBytesOptions(size: number): BytesOptions {
  // increase precision for larger files
  const GB = 1_000_000_000
  const MB = 1_000_000
  const decimalPlaces = size >= GB ? 2 : size >= MB ? 1 : 0

  return { decimalPlaces }
}

type FileData = {
  icon: ReactElement
  name: string
  size: number
  href: string
}

function useFileData(value: FileValue): FileData | null {
  const [objectUrl, setObjectUrl] = useState<string>('')
  useEffect(() => {
    if (value.kind === 'upload') {
      const objectUrl = URL.createObjectURL(value.data.file)
      setObjectUrl(objectUrl)
      return () => {
        URL.revokeObjectURL(objectUrl)
      }
    }
  }, [value])
  switch (value.kind) {
    case 'from-server':
      return {
        icon: getFileIcon(value.data.filename),
        name: value.data.filename,
        size: value.data.filesize,
        href: value.data.src,
      }
    case 'upload':
      return {
        icon: fileUpIcon,
        name: value.data.file.name,
        size: value.data.file.size,
        href: objectUrl,
      }
    default:
      return null
  }
}

// prettier-ignore
const FILE_TYPES = {
  audio: new Set(['aac', 'aiff', 'alac', 'flac', 'm4a', 'mp3', 'ogg', 'wav', 'wma']),
  binary: new Set(['bin', 'dat', 'exe', 'iso', 'pkg', 'rar', 'tar', 'zip']),
  code: new Set(['c', 'cpp', 'cs', 'css', 'go', 'html', 'java', 'js', 'jsx', 'less', 'php', 'py', 'rb', 'rs', 'scss', 'ts', 'tsx', 'xml']),
  image: new Set(['avif', 'bmp', 'gif', 'heic', 'ico', 'jpeg', 'jpg', 'png', 'svg', 'tiff', 'webp']),
  json: new Set(['geojson', 'json', 'json5', 'jsonld']),
  spreadsheet: new Set(['csv', 'ods', 'tsv', 'xls', 'xlsx']),
  text: new Set(['doc', 'docx', 'eml', 'log', 'md', 'msg', 'odt', 'pdf', 'rtf', 'tex', 'txt']),
  video: new Set(['avi', 'flv', 'm4v', 'mkv', 'mov', 'mp4', 'ogg', 'webm', 'wmv']),
}

function getFileIcon(filename: string) {
  const extension = getExtension(filename)

  if (FILE_TYPES.audio.has(extension)) return fileAudioIcon
  if (FILE_TYPES.binary.has(extension)) return fileDigitIcon
  if (FILE_TYPES.code.has(extension)) return fileCodeIcon
  if (FILE_TYPES.image.has(extension)) return fileImageIcon
  if (FILE_TYPES.json.has(extension)) return fileJsonIcon
  if (FILE_TYPES.spreadsheet.has(extension)) return fileSpreadsheetIcon
  if (FILE_TYPES.text.has(extension)) return fileTextIcon
  if (FILE_TYPES.video.has(extension)) return fileVideoIcon

  return fileIcon
}
function getExtension(filename: string) {
  // `extname` returns e.g. ".mov", remove leading dot to match "mov"
  return extname(filename).slice(1).toLowerCase()
}

export function useTrimStartStyles() {
  const { direction } = useLocale()
  return {
    direction: direction === 'rtl' ? 'ltr' : 'rtl',
    display: 'block',
    overflow: 'hidden',
    textAlign: direction === 'rtl' ? 'right' : 'left',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  } as const
}

function createErrorMessage(value: FileValue) {
  if (value.kind === 'upload') {
    return validateFile(value.data)
  }
}

export function validateFile({ validity }: { validity: ValidityState }) {
  if (!validity.valid) {
    return 'Something went wrong, please reload and try again.'
  }
}
</file>

<file path="packages/core/src/fields/types/file/views/index.tsx">
import type { CellComponent, FieldController, FieldControllerConfig } from '../../../../types'

import { validateFile } from './Field'

export { Field } from './Field'

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  if (!value) return null
  return (
    <div
      style={{
        alignItems: 'center',
        display: 'flex',
        height: 24,
        lineHeight: 0,
        width: 24,
      }}
    >
      {value.filename}
    </div>
  )
}

type FileData = {
  src: string
  filesize: number
  filename: string
}

export type FileValue =
  | { kind: 'empty' }
  | {
      kind: 'from-server'
      data: FileData
    }
  | {
      kind: 'upload'
      data: {
        file: File
        validity: ValidityState
      }
      previous: FileValue
    }
  | { kind: 'remove'; previous?: Exclude<FileValue, { kind: 'remove' }> }

type FileController = FieldController<FileValue>

export function controller(config: FieldControllerConfig): FileController {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: `${config.path} {
        url
        filename
        filesize
      }`,
    defaultValue: { kind: 'empty' },
    deserialize(item) {
      const value = item[config.path]
      if (!value) return { kind: 'empty' }
      return {
        kind: 'from-server',
        data: {
          src: value.url,
          filename: value.filename,
          ref: value.ref,
          filesize: value.filesize,
          storage: value.storage,
        },
      }
    },
    validate(value): boolean {
      return value.kind !== 'upload' || validateFile(value.data) === undefined
    },
    serialize(value) {
      if (value.kind === 'upload') {
        return { [config.path]: { upload: value.data.file } }
      }
      if (value.kind === 'remove') {
        return { [config.path]: null }
      }
      return {}
    },
  }
}
</file>

<file path="packages/core/src/fields/types/file/index.ts">
import type {
  BaseKeystoneTypeInfo,
  BaseListTypeInfo,
  CommonFieldConfig,
  FieldHooks,
  FieldTypeFunc,
  KeystoneContext,
  MaybePromise,
  StorageStrategy,
} from '../../../types'
import { fieldType } from '../../../types'
import { g } from '../../..'
import { merge } from '../../resolve-hooks'
import type { InferValueFromArg, InferValueFromInputType } from '@graphql-ts/schema'
import { randomBytes } from 'node:crypto'

export type FieldTypeInfo = {
  item: undefined
  inputs: {
    create: InferValueFromInputType<typeof FileFieldInput> | null | undefined
    update: InferValueFromInputType<typeof FileFieldInput> | null | undefined
    where: undefined
    uniqueWhere: undefined
    orderBy: undefined
  }
  prisma: {
    create: undefined
    update: undefined
  }
}

export type FileFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> & {
  storage: StorageStrategy<ListTypeInfo['all']>
  transformName?: (originalFilename: string) => MaybePromise<string>
  db?: {
    extendPrismaSchema?: (field: string) => string
  }
}

const FileFieldInput = g.inputObject({
  name: 'FileFieldInput',
  fields: {
    upload: g.arg({ type: g.nonNull(g.Upload) }),
  },
})

const inputArg = g.arg({ type: FileFieldInput })

const FileFieldOutput = g.object<{
  filename: string
  filesize: number
  url: (_args: {}, context: KeystoneContext) => Promise<string>
}>()({
  name: 'FileFieldOutput',
  fields: {
    filename: g.field({ type: g.nonNull(g.String) }),
    filesize: g.field({ type: g.nonNull(g.Int) }),
    url: g.field({ type: g.nonNull(g.String) }),
  },
})

async function inputResolver(
  storage: StorageStrategy<BaseKeystoneTypeInfo>,
  transformName: (originalFilename: string) => Promise<string> | string,
  context: KeystoneContext,
  data: InferValueFromArg<typeof inputArg>
) {
  if (data === null || data === undefined) return { filename: data, filesize: data }
  const upload = await data.upload
  const stream = upload.createReadStream()
  let filesize = 0
  stream.on('data', data => {
    filesize += data.length
  })

  const filename = await transformName(upload.filename)
  await storage.put(filename, stream, { contentType: 'application/octet-stream' }, context)
  return { filename, filesize }
}

export function file<ListTypeInfo extends BaseListTypeInfo>(
  config: FileFieldConfig<ListTypeInfo>
): FieldTypeFunc<ListTypeInfo> {
  const { transformName = defaultTransformName } = config
  return meta => {
    const { fieldKey } = meta

    if ('isIndexed' in config) {
      throw Error("isIndexed: 'unique' is not a supported option for field type file")
    }

    const afterOperationResolver: Extract<
      FieldHooks<BaseListTypeInfo, FieldTypeInfo>['afterOperation'],
      (args: any) => any
    > = async function afterOperationResolver(args) {
      if (args.operation === 'update' || args.operation === 'delete') {
        const filenameKey = `${fieldKey}_filename`
        const oldFilename = args.originalItem[filenameKey] as string | null | undefined
        const newFilename = args.item?.[filenameKey] as string | null | undefined

        // this will occur on an update where a file already existed but has been
        // changed, or on a delete, where there is no longer an item
        // but not when the old and new filenames are the same (in that case, presumably the file has been overwritten)
        if (typeof oldFilename === 'string' && oldFilename !== newFilename) {
          await config.storage.delete(oldFilename, args.context)
        }
      }
    }

    return fieldType({
      kind: 'multi',
      extendPrismaSchema: config.db?.extendPrismaSchema,
      fields: {
        filesize: { kind: 'scalar', scalar: 'Int', mode: 'optional' },
        filename: { kind: 'scalar', scalar: 'String', mode: 'optional' },
      },
    })({
      ...config,
      hooks: {
        ...config.hooks,
        afterOperation: merge(config.hooks?.afterOperation, {
          update: afterOperationResolver,
          delete: afterOperationResolver,
        }),
      },
      input: {
        create: {
          arg: inputArg,
          resolve: (data, context) => inputResolver(config.storage, transformName, context, data),
        },
        update: {
          arg: inputArg,
          resolve: (data, context) => inputResolver(config.storage, transformName, context, data),
        },
      },
      output: g.field({
        type: FileFieldOutput,
        resolve({ value: { filesize, filename } }) {
          if (filename === null) return null
          if (filesize === null) return null
          return {
            filename,
            filesize,
            url: async (_, context) => config.storage.url(filename, context),
          }
        },
      }),
      __ksTelemetryFieldTypeName: '@keystone-6/file',
      views: '@keystone-6/core/fields/types/file/views',
    })
  }
}

// appends a 128-bit random identifier to the filename to prevent guessing
function defaultTransformName(path: string) {
  // this regex lazily matches for any characters that aren't a new line
  // it then optionally matches the last instance of a "." symbol
  // followed by any alphanumerical character before the end of the string
  const [, name, ext] = path.match(/^([^:\n].*?)(\.[A-Za-z0-9]{0,10})?$/) as RegExpMatchArray

  const id = randomBytes(16).toString('base64url')
  const urlSafeName = name.replace(/[^A-Za-z0-9]/g, '-')
  if (ext) return `${urlSafeName}-${id}${ext}`
  return `${urlSafeName}-${id}`
}
</file>

<file path="packages/core/src/fields/types/float/views/index.tsx">
import { useState } from 'react'

import { TextField } from '@keystar/ui/text-field'

import {
  type FieldController,
  type FieldControllerConfig,
  type FieldProps,
  type SimpleFieldTypeInfo,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'

const TYPE_OPERATOR_MAP = {
  equals: '=',
  not: '≠',
  gt: '>',
  lt: '<',
  gte: '≥',
  lte: '≤',
} as const

type Value =
  | { kind: 'create'; value: string | null }
  | { kind: 'update'; initial: string | null; value: string | null }

type Validation = {
  min: number | null
  max: number | null
}

function validate_(
  value: Value,
  validation: Validation,
  isRequired: boolean,
  label: string
): string | undefined {
  const { value: input, kind } = value
  if (kind === 'update' && value.initial === null && input === null) return
  if (isRequired && input === null) return `${label} is required`
  if (typeof input !== 'string') return
  const v = parseFloat(input)
  if (Number.isNaN(v)) return `${label} is not a valid float`
  if (validation.min != null && v < validation.min)
    return `${label} must be greater than or equal to ${validation.min}`
  if (validation.max != null && v > validation.max)
    return `${label} must be less than or equal to ${validation.max}`
}

export function controller(
  config: FieldControllerConfig<{
    validation: Validation
    defaultValue: string | null
  }>
): FieldController<Value, string | null, SimpleFieldTypeInfo<'Float'>['inputs']['where']> & {
  validation: Validation
} {
  const validate = (value: Value, opts: { isRequired: boolean }) => {
    return validate_(value, config.fieldMeta.validation, opts.isRequired, config.label)
  }

  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    validation: config.fieldMeta.validation,
    defaultValue: { kind: 'create', value: config.fieldMeta.defaultValue },
    deserialize: data => ({ kind: 'update', value: data[config.path], initial: data[config.path] }),
    serialize: value => {
      const v = value.value !== null ? parseFloat(value.value) : null
      return { [config.path]: Number.isFinite(v) ? v : null }
    },
    filter: {
      Filter(props) {
        const {
          autoFocus,
          context,
          forceValidation,
          typeLabel,
          onChange,
          type,
          value,
          ...otherProps
        } = props
        const [isDirty, setDirty] = useState(false)
        if (type === 'empty' || type === 'not_empty') return null

        const labelProps =
          context === 'add' ? { label: config.label, description: typeLabel } : { label: typeLabel }

        return (
          <TextField
            {...otherProps}
            {...labelProps}
            autoFocus={autoFocus}
            errorMessage={
              (forceValidation || isDirty) &&
              !validate({ kind: 'update', initial: null, value }, { isRequired: true })
                ? 'Required'
                : null
            }
            inputMode="numeric"
            width="auto"
            onBlur={() => setDirty(true)}
            onChange={x => onChange?.(x === '' ? null : x)}
            value={value ?? ''}
          />
        )
      },

      graphql: ({ type, value }) => {
        if (type === 'empty') return { [config.path]: { equals: null } }
        if (type === 'not_empty') return { [config.path]: { not: { equals: null } } }
        const val = value === null ? null : parseFloat(value)
        if (type === 'not') return { [config.path]: { not: { equals: val } } }
        return { [config.path]: { [type]: val } }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (type === 'equals' && value === null) {
            return [{ type: 'empty', value: null }]
          }
          if (!value) return []
          if (type === 'equals') return { type: 'equals', value: value.toString() }
          if (type === 'not') {
            if (value.equals === null) return { type: 'not_empty', value: null }
            if (value.equals === undefined) return []
            return { type: 'not', value: value.equals.toString() }
          }
          if (type === 'gt' || type === 'gte' || type === 'lt' || type === 'lte') {
            return { type, value: value.toString() }
          }
          return []
        })
      },
      Label({ label, type, value }) {
        if (type === 'empty' || type === 'not_empty') return label.toLocaleLowerCase()
        const operator = TYPE_OPERATOR_MAP[type as keyof typeof TYPE_OPERATOR_MAP]
        return `${operator} ${value}`
      },
      types: {
        equals: {
          label: 'Is exactly',
          initialValue: null,
        },
        not: {
          label: 'Is not exactly',
          initialValue: null,
        },
        gt: {
          label: 'Is greater than',
          initialValue: null,
        },
        lt: {
          label: 'Is less than',
          initialValue: null,
        },
        gte: {
          label: 'Is greater than or equal to',
          initialValue: null,
        },
        lte: {
          label: 'Is less than or equal to',
          initialValue: null,
        },
        empty: {
          label: 'Is empty',
          initialValue: null,
        },
        not_empty: {
          label: 'Is not empty',
          initialValue: null,
        },
      },
    },

    validate: (value, opts) => validate(value, opts) === undefined,
  }
}

export function Field({
  field,
  value,
  onChange,
  autoFocus,
  forceValidation,
  isRequired,
}: FieldProps<typeof controller>) {
  const [isDirty, setDirty] = useState(false)
  const isReadOnly = !onChange

  const validate = (value: Value) => {
    return validate_(value, field.validation, isRequired, field.label)
  }

  return (
    <TextField
      autoFocus={autoFocus}
      description={field.description}
      label={field.label}
      errorMessage={(forceValidation || isDirty) && validate(value)}
      isReadOnly={isReadOnly}
      isRequired={isRequired}
      inputMode="numeric"
      width="alias.singleLineWidth"
      onBlur={() => setDirty(true)}
      onChange={x => onChange?.({ ...value, value: x === '' ? null : x })}
      value={value.value ?? ''}
    />
  )
}
</file>

<file path="packages/core/src/fields/types/float/index.ts">
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type FieldTypeFunc,
  type CommonFieldConfig,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { filters } from '../../filters'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import type { controller } from './views'

export type FloatFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'Float'>
> & {
  isIndexed?: boolean | 'unique'
  defaultValue?: number | null
  validation?: {
    isRequired?: boolean
    min?: number
    max?: number
  }
  db?: {
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
  }
}

export function float<ListTypeInfo extends BaseListTypeInfo>(
  config: FloatFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { defaultValue: defaultValue_, isIndexed, validation = {} } = config

  const { isRequired = false, min, max } = validation
  const defaultValue = defaultValue_ ?? null

  return meta => {
    if (defaultValue !== null && !Number.isFinite(defaultValue)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a default value of: ${defaultValue} but it must be a valid finite number`
      )
    }
    if (min !== undefined && !Number.isFinite(min)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.min: ${min} but it must be a valid finite number`
      )
    }
    if (max !== undefined && !Number.isFinite(max)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.max: ${max} but it must be a valid finite number`
      )
    }
    if (min !== undefined && max !== undefined && min > max) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a validation.max that is less than the validation.min, and therefore has no valid options`
      )
    }

    const hasAdditionalValidation = min !== undefined || max !== undefined
    const { mode, validate } = makeValidateHook(
      meta,
      config,
      hasAdditionalValidation
        ? ({ resolvedData, operation, addValidationError }) => {
            if (operation === 'delete') return

            const value = resolvedData[meta.fieldKey]
            if (typeof value === 'number') {
              if (min !== undefined && value < min) {
                addValidationError(`value must be greater than or equal to ${min}`)
              }

              if (max !== undefined && value > max) {
                addValidationError(`value must be less than or equal to ${max}`)
              }
            }
          }
        : undefined
    )

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: 'Float',
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      default:
        typeof defaultValue === 'number' ? { kind: 'literal', value: defaultValue } : undefined,
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, isRequired),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.Float }) } : undefined,
        where: {
          arg: g.arg({ type: filters[meta.provider].Float[mode] }),
          resolve: mode === 'optional' ? filters.resolveCommon : undefined,
        },
        create: {
          arg: g.arg({
            type: g.Float,
            defaultValue: typeof defaultValue === 'number' ? defaultValue : undefined,
          }),
          resolve(value) {
            if (value === undefined) return defaultValue
            return value
          },
        },
        update: { arg: g.arg({ type: g.Float }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({ type: g.Float }),
      __ksTelemetryFieldTypeName: '@keystone-6/float',
      views: '@keystone-6/core/fields/types/float/views',
      getAdminMeta(): Parameters<typeof controller>[0]['fieldMeta'] {
        return {
          validation: {
            min: min ?? null,
            max: max ?? null,
          },
          defaultValue: defaultValue === null ? null : defaultValue.toString(),
        }
      },
    })
  }
}
</file>

<file path="packages/core/src/fields/types/image/views/Field.tsx">
import {
  type PropsWithChildren,
  type SyntheticEvent,
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

import { ActionButton } from '@keystar/ui/button'
import { Field as KeystarField } from '@keystar/ui/field'
import { HStack, VStack } from '@keystar/ui/layout'
import { css, tokenSchema, transition } from '@keystar/ui/style'
import { Text } from '@keystar/ui/typography'

import type { FieldProps } from '../../../../types'
import { formatBytes, useTrimStartStyles } from '../../file/views/Field'
import type { ImageValue } from './index'
import { type controller, validateImage } from '.'

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, onChange, value } = props
  const inputRef = useRef<HTMLInputElement | null>(null)
  const errorMessage = validateImage(field.extensions, value)

  const onUploadChange = ({
    currentTarget: { validity, files },
  }: SyntheticEvent<HTMLInputElement>) => {
    const file = files?.[0]
    if (!file) return // bail if the user cancels from the file browser
    onChange?.({
      kind: 'upload',
      data: { file, validity },
      previous: value,
    })
  }
  const onFileTrigger = () => {
    inputRef.current?.click()
  }

  // Generate a random input key when the value changes, to ensure the file input is unmounted and
  // remounted (this is the only way to reset its value and ensure onChange will fire again if
  // the user selects the same file again)
  const inputKey = useMemo(() => Math.random(), [value])
  const accept = useMemo(
    () => field.extensions.map(ext => [`.${ext}`, `image/${ext}`].join(', ')).join(', '),
    []
  )

  return (
    <KeystarField label={field.label} description={field.description} errorMessage={errorMessage}>
      {inputProps => (
        <Fragment>
          <ImageView
            isInvalid={Boolean(errorMessage)}
            onFileTrigger={onFileTrigger}
            onChange={onChange}
            value={value}
          />
          <input
            {...inputProps}
            accept={accept}
            autoComplete="off"
            autoFocus={autoFocus}
            disabled={onChange === undefined}
            hidden
            key={inputKey}
            name={field.path}
            onChange={onUploadChange}
            ref={inputRef}
            type="file"
          />
        </Fragment>
      )}
    </KeystarField>
  )
}

function ImageView(props: {
  onFileTrigger: () => void
  isInvalid?: boolean
  onChange?: (value: ImageValue) => void
  value: ImageValue
}) {
  const { isInvalid, onFileTrigger, onChange, value } = props
  const imageData = useImageData(value)

  return (
    <VStack gap="regular">
      {isInvalid || !imageData ? null : (
        <ImageDetails {...imageData}>
          {onChange && (
            <HStack gap="regular" alignItems="center" marginTop="auto">
              <ActionButton onPress={onFileTrigger}>Change</ActionButton>
              {value.kind === 'from-server' && (
                <ActionButton
                  prominence="low"
                  onPress={() => {
                    onChange({ kind: 'remove', previous: value })
                  }}
                >
                  Remove
                </ActionButton>
              )}
              {value.kind === 'upload' && (
                <ActionButton
                  prominence="low"
                  onPress={() => {
                    onChange(value.previous)
                  }}
                >
                  Cancel
                </ActionButton>
              )}
            </HStack>
          )}
        </ImageDetails>
      )}

      {!imageData && (
        <HStack gap="regular" alignItems="center">
          <ActionButton isDisabled={onChange === undefined} onPress={onFileTrigger}>
            Upload
          </ActionButton>
          {value.kind === 'remove' && value.previous && (
            <ActionButton
              prominence="low"
              onPress={() => {
                if (value.previous !== undefined) {
                  onChange?.(value?.previous)
                }
              }}
            >
              Undo removal
            </ActionButton>
          )}
        </HStack>
      )}
    </VStack>
  )
}

function useObjectURL(fileData: File | undefined) {
  const [objectURL, setObjectURL] = useState<string | undefined>(undefined)
  useEffect(() => {
    if (!fileData) return
    const url = URL.createObjectURL(fileData)
    setObjectURL(url)
    return () => URL.revokeObjectURL(url)
  }, [fileData])
  return objectURL
}

// ==============================
// Styled Components
// ==============================

const SMALL_CONTAINER_MAX = `@container (max-width: 419px)`
const SMALL_CONTAINER_MIN = `@container (min-width: 420px)`

function ImageDetails(props: PropsWithChildren<ImageData>) {
  const trimStartStyles = useTrimStartStyles()

  // hide content until the uploaded image until it's available; use dimensions
  // as an indicator since they're set on load.
  const loadedStyles = {
    opacity: props.height && props.width ? 1 : 0,
    transition: transition('opacity'),
  }

  return (
    <div
      className={css({
        backgroundColor: tokenSchema.color.background.canvas,
        border: `1px solid ${tokenSchema.color.border.neutral}`,
        borderRadius: tokenSchema.size.radius.regular,
        minHeight: tokenSchema.size.scale['1600'],
        contain: 'paint', // crop img to border radius
        display: 'flex',

        [SMALL_CONTAINER_MAX]: {
          flexDirection: 'column',
        },
      })}
    >
      <img
        onLoad={props.onLoad}
        className={css({
          maxHeight: tokenSchema.size.scale[6000], // constrain tall/narrow images
          maxWidth: '100%', // constrain to container width
          minWidth: 0, // allow the image to shrink properly
          objectFit: 'cover', // fill available space w/o distorting

          [SMALL_CONTAINER_MIN]: {
            height: tokenSchema.size.scale['1600'],
          },
        })}
        style={loadedStyles}
        alt="preview of the upload"
        src={props.url}
      />
      <VStack
        gap="medium"
        padding="large"
        flex
        UNSAFE_className={css({
          // ensure the metadata doesn't shrink too much, 40% allows more
          // breathing room where available with a hard limit pixel value
          minWidth: `max(${tokenSchema.size.alias.singleLineWidth}, 40%)`,
        })}
        UNSAFE_style={loadedStyles}
      >
        {props.name ? (
          <Text>
            <span className={css(trimStartStyles)} title={props.name}>
              {props.name}
            </span>
          </Text>
        ) : null}
        {props.filesize ? (
          <Text size="small" color="neutralSecondary" overflow="unset">
            {formatBytes(props.filesize)} &middot; {props.width}
            &#8239;&times;&#8239;
            {props.height}
          </Text>
        ) : null}

        {/* field controls dependant on value type */}
        {props.children}
      </VStack>
    </div>
  )
}

type ImageData = {
  name?: string
  url: string
  filesize?: number
  width?: number
  height?: number
  onLoad: (event: SyntheticEvent<HTMLImageElement>) => void
}

function useImageData(value: ImageValue): ImageData | null {
  // only relevant for uploaded images, but we must observe the rules of hooks
  // so these can't be called conditionally.
  const imagePathFromUpload = useObjectURL(value.kind === 'upload' ? value.data.file : undefined)
  const [dimensions, setDimensions] = useState({ height: 0, width: 0 })
  const onLoad = useCallback(
    (event: SyntheticEvent<HTMLImageElement>) => {
      if (value.kind === 'upload') {
        setDimensions({
          height: event.currentTarget.naturalHeight,
          width: event.currentTarget.naturalWidth,
        })
      }
    },
    [value.kind]
  )

  // reset dimensions when the user cancels the upload. we use the dimensions as
  // a signal that the image has loaded.
  useEffect(() => {
    if (value.kind !== 'upload') {
      setDimensions({ height: 0, width: 0 })
    }
  }, [value.kind])

  switch (value.kind) {
    case 'from-server':
      return {
        url: value.data.url,
        name: `${value.data.id}.${value.data.extension}`,
        filesize: value.data.filesize,
        width: value.data.width,
        height: value.data.height,
        onLoad,
      } as const

    case 'upload':
      return {
        // always string for simpler types, should be unreachable if the file selection fails validation.
        url: imagePathFromUpload || '',
        name: value.data.file.name,
        filesize: value.data.file.size,
        width: dimensions.width,
        height: dimensions.height,
        onLoad,
      } as const
  }

  return null
}
</file>

<file path="packages/core/src/fields/types/image/views/index.tsx">
import type { CellComponent, FieldControllerConfig } from '../../../../types'
import { SUPPORTED_IMAGE_EXTENSIONS } from '../utils'

export { Field } from './Field'

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  if (!value) return null
  return (
    <div
      style={{
        alignItems: 'center',
        display: 'flex',
        height: 24,
        lineHeight: 0,
        width: 24,
      }}
    >
      <img style={{ maxHeight: '100%', maxWidth: '100%' }} src={value.url} />
    </div>
  )
}

export type ImageValue =
  | { kind: 'empty' }
  | {
      kind: 'from-server'
      data: {
        id: string
        url: string
        extension: string
        filesize: number
        width: number
        height: number
      }
    }
  | {
      kind: 'upload'
      data: {
        file: File
        validity: ValidityState
      }
      previous: ImageValue
    }
  | { kind: 'remove'; previous?: Exclude<ImageValue, { kind: 'remove' }> }

export function validateImage(extensions: readonly string[], v: ImageValue) {
  if (v.kind !== 'upload') return
  if (!v.data.validity.valid) return 'Something went wrong, please reload and try again.'

  // check if the file is actually an image
  if (!v.data.file.type.includes('image')) {
    return `Sorry, that file type isn't accepted. Please try ${extensions.join(', ')}`
  }
}

export function controller(config: FieldControllerConfig) {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: `${config.path} {
      id
      url
      extension
      filesize
      width
      height
    }`,
    defaultValue: { kind: 'empty' },
    extensions: SUPPORTED_IMAGE_EXTENSIONS,
    deserialize(item: any): ImageValue {
      const value = item[config.path]
      if (!value) return { kind: 'empty' }
      return {
        kind: 'from-server',
        data: value,
      }
    },
    validate(value: ImageValue): boolean {
      return validateImage(SUPPORTED_IMAGE_EXTENSIONS, value) === undefined
    },
    serialize(value: ImageValue) {
      if (value.kind === 'upload') {
        return { [config.path]: { upload: value.data.file } }
      }
      if (value.kind === 'remove') {
        return { [config.path]: null }
      }
      return {}
    },
  }
}
</file>

<file path="packages/core/src/fields/types/image/index.ts">
import type {
  BaseListTypeInfo,
  FieldTypeFunc,
  CommonFieldConfig,
  KeystoneContext,
  BaseKeystoneTypeInfo,
  MaybePromise,
  FieldHooks,
  StorageStrategy,
} from '../../../types'
import { fieldType } from '../../../types'
import { g } from '../../..'
import { SUPPORTED_IMAGE_EXTENSIONS } from './utils'
import { merge } from '../../resolve-hooks'
import type { InferValueFromArg, InferValueFromInputType } from '@graphql-ts/schema'
import { randomBytes } from 'node:crypto'
import type { ImageExtension } from './internal-utils'
import { getBytesFromStream, getImageMetadata, teeStream } from './internal-utils'

export type FieldTypeInfo = {
  item: undefined
  inputs: {
    create: InferValueFromInputType<typeof ImageFieldInput> | null | undefined
    update: InferValueFromInputType<typeof ImageFieldInput> | null | undefined
    where: undefined
    uniqueWhere: undefined
    orderBy: undefined
  }
  prisma: {
    create: undefined
    update: undefined
  }
}

export type ImageFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> & {
  storage: StorageStrategy<ListTypeInfo['all']>
  transformName?: (originalFilename: string, extension: string) => MaybePromise<string>
  db?: {
    extendPrismaSchema?: (field: string) => string
  }
}

// TODO: dynamic
const ImageExtensionEnum = g.enum({
  name: 'ImageExtension',
  values: g.enumValues(SUPPORTED_IMAGE_EXTENSIONS),
})

const ImageFieldInput = g.inputObject({
  name: 'ImageFieldInput',
  fields: {
    upload: g.arg({ type: g.nonNull(g.Upload) }),
  },
})

const inputArg = g.arg({ type: ImageFieldInput })

type ImageData = {
  id: string
  extension: ImageExtension
  filesize: number
  width: number
  height: number
  url: (_args: {}, context: KeystoneContext) => Promise<string>
}

const ImageFieldOutput = g.object<ImageData>()({
  name: 'ImageFieldOutput',
  fields: {
    id: g.field({ type: g.nonNull(g.ID) }),
    url: g.field({ type: g.nonNull(g.String) }),
    extension: g.field({ type: g.nonNull(ImageExtensionEnum) }),
    filesize: g.field({ type: g.nonNull(g.Int) }),
    width: g.field({ type: g.nonNull(g.Int) }),
    height: g.field({ type: g.nonNull(g.Int) }),
  },
})

// this is a conservative estimate of the number of bytes
// that we need to determine the image metadata
// since 1Kib in memory will be fine
const bytesToDetermineImageMetadata = 1024

async function inputResolver(
  storage: StorageStrategy<BaseKeystoneTypeInfo>,
  transformName: (originalFilename: string, extension: string) => MaybePromise<string>,
  context: KeystoneContext,
  data: InferValueFromArg<typeof inputArg>
): Promise<{
  id: string | null | undefined
  extension: ImageExtension | null | undefined
  filesize: number | null | undefined
  width: number | null | undefined
  height: number | null | undefined
}> {
  if (data === null || data === undefined) {
    return {
      id: data,
      extension: data,
      filesize: data,
      width: data,
      height: data,
    }
  }

  const upload = await data.upload
  let filesize = 0
  const _readable = upload.createReadStream()
  const [readableForFilesize, _readable2] = teeStream(_readable)
  const [readableForMetadata, readableForUpload] = teeStream(_readable2)
  readableForFilesize.on('data', data => {
    filesize += data.length
  })

  const buffer = await getBytesFromStream(readableForMetadata, bytesToDetermineImageMetadata)
  const metadata = getImageMetadata(buffer)
  if (!metadata) {
    throw new Error('File type not found')
  }
  const id = await transformName(upload.filename, metadata.extension)
  await storage.put(
    `${id}.${metadata.extension}`,
    readableForUpload,
    {
      contentType: {
        png: 'image/png',
        webp: 'image/webp',
        gif: 'image/gif',
        jpg: 'image/jpeg',
      }[metadata.extension],
    },
    context
  )
  return {
    filesize,
    id,
    extension: metadata.extension,
    height: metadata.height,
    width: metadata.width,
  }
}

const extensionsSet = new Set<string>(SUPPORTED_IMAGE_EXTENSIONS)

function isValidImageExtension(extension: string): extension is ImageExtension {
  return extensionsSet.has(extension)
}

export function image<ListTypeInfo extends BaseListTypeInfo>(
  config: ImageFieldConfig<ListTypeInfo>
): FieldTypeFunc<ListTypeInfo> {
  const { transformName = defaultTransformName } = config
  return meta => {
    const { fieldKey } = meta

    if ('isIndexed' in config) {
      throw Error("isIndexed: 'unique' is not a supported option for field type image")
    }

    const afterOperationResolver: Extract<
      FieldHooks<BaseListTypeInfo, FieldTypeInfo>['afterOperation'],
      (args: any) => any
    > = async function afterOperationResolver(args) {
      if (args.operation === 'update' || args.operation === 'delete') {
        const idKey = `${fieldKey}_id`
        const oldId = args.originalItem?.[idKey] as string | null | undefined
        const newId = args.item?.[idKey] as string | null | undefined
        const extensionKey = `${fieldKey}_extension`
        const oldExtension = args.originalItem?.[extensionKey] as string | null | undefined
        const newExtension = args.item?.[extensionKey] as string | null | undefined
        // this will occur on an update where an image already existed but has been
        // changed, or on a delete, where there is no longer an item
        if (
          typeof oldId === 'string' &&
          typeof oldExtension === 'string' &&
          isValidImageExtension(oldExtension) &&
          (oldId !== newId || oldExtension !== newExtension)
        ) {
          await config.storage.delete(`${oldId}.${oldExtension}`, args.context)
        }
      }
    }

    return fieldType({
      kind: 'multi',
      extendPrismaSchema: config.db?.extendPrismaSchema,
      fields: {
        id: { kind: 'scalar', scalar: 'String', mode: 'optional' },
        extension: { kind: 'scalar', scalar: 'String', mode: 'optional' },
        filesize: { kind: 'scalar', scalar: 'Int', mode: 'optional' },
        width: { kind: 'scalar', scalar: 'Int', mode: 'optional' },
        height: { kind: 'scalar', scalar: 'Int', mode: 'optional' },
      },
    })({
      ...config,
      hooks: {
        ...config.hooks,
        afterOperation: merge(config.hooks?.afterOperation, {
          update: afterOperationResolver,
          delete: afterOperationResolver,
        }),
      },
      input: {
        create: {
          arg: inputArg,
          resolve: (data, context) => inputResolver(config.storage, transformName, context, data),
        },
        update: {
          arg: inputArg,
          resolve: (data, context) => inputResolver(config.storage, transformName, context, data),
        },
      },
      output: g.field({
        type: ImageFieldOutput,
        resolve({ value: { id, extension, filesize, width, height } }): ImageData | null {
          if (id === null) return null
          if (extension === null) return null
          if (filesize === null) return null
          if (width === null) return null
          if (height === null) return null
          if (!isValidImageExtension(extension)) return null // TODO: dynamic

          return {
            id,
            filesize,
            width,
            height,
            extension,
            url: async (_, context) => config.storage.url(`${id}.${extension}`, context),
          }
        },
      }),
      __ksTelemetryFieldTypeName: '@keystone-6/image',
      views: '@keystone-6/core/fields/types/image/views',
    })
  }
}

function defaultTransformName(path: string) {
  return randomBytes(16).toString('base64url')
}
</file>

<file path="packages/core/src/fields/types/image/internal-utils.ts">
import { JPG } from 'image-size/types/jpg'
import { PNG } from 'image-size/types/png'
import { WEBP } from 'image-size/types/webp'
import { GIF } from 'image-size/types/gif'
import { PassThrough, type Readable } from 'node:stream'

export function getImageMetadata(bytes: Uint8Array) {
  for (const [type, checker] of types) {
    if (checker.validate(bytes)) {
      const result = checker.calculate(bytes)
      if (result.width === undefined || result.height === undefined) {
        return
      }
      return {
        width: result.width,
        height: result.height,
        extension: type,
      }
    }
  }
}

const types = [
  ['jpg', JPG],
  ['png', PNG],
  ['webp', WEBP],
  ['gif', GIF],
] as const

export type ImageExtension = (typeof types)[number][0]

export async function getBytesFromStream(stream: Readable, maxBytes: number): Promise<Buffer> {
  let chunks: Buffer[] = []
  for await (const chunk of stream) {
    maxBytes -= chunk.length
    chunks.push(chunk)
    if (maxBytes <= 0) {
      break
    }
  }
  stream.destroy()
  return Buffer.concat(chunks)
}

export function teeStream(source: Readable): [Readable, Readable] {
  const passThrough1 = new PassThrough()
  const passThrough2 = new PassThrough()

  source.pipe(passThrough1)
  source.pipe(passThrough2)

  source.on('error', err => {
    passThrough1.destroy(err)
    passThrough2.destroy(err)
  })

  return [passThrough1, passThrough2]
}
</file>

<file path="packages/core/src/fields/types/image/utils.ts">
export const SUPPORTED_IMAGE_EXTENSIONS = ['jpg', 'png', 'webp', 'gif'] as const
</file>

<file path="packages/core/src/fields/types/integer/views/index.tsx">
import { useState } from 'react'

import { ContextualHelp } from '@keystar/ui/contextual-help'
import { Content } from '@keystar/ui/slots'
import { NumberField } from '@keystar/ui/number-field'

import { Heading, Text } from '@keystar/ui/typography'

import type {
  FieldController,
  FieldControllerConfig,
  FieldProps,
  SimpleFieldTypeInfo,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'

// TODO: extract
const TYPE_OPERATOR_MAP = {
  equals: '=',
  not: '≠',
  gt: '>',
  lt: '<',
  gte: '≥',
  lte: '≤',
} as const

type Value =
  | { kind: 'create'; value: number | null }
  | { kind: 'update'; initial: number | null; value: number | null }

type Validation = {
  min: number
  max: number
}

function validate_(
  value: Value,
  validation: Validation,
  isRequired: boolean,
  label: string,
  hasAutoIncrementDefault: boolean
): string | undefined {
  const { value: input, kind } = value
  if (kind === 'create' && hasAutoIncrementDefault && input === null) return
  if (kind === 'update' && value.initial === null && input === null) return
  if (isRequired && input === null) return `${label} is required`
  if (typeof input !== 'number') return
  const v = input
  if (!Number.isInteger(v)) return `${label} is not a valid integer`
  if (validation.min !== undefined && v < validation.min)
    return `${label} must be greater than or equal to ${validation.min}`
  if (validation.max !== undefined && v > validation.max)
    return `${label} must be less than or equal to ${validation.max}`
}

export function controller(
  config: FieldControllerConfig<{
    validation: Validation
    defaultValue: number | null | 'autoincrement'
  }>
): FieldController<Value, number | null, SimpleFieldTypeInfo<'Int'>['inputs']['where']> & {
  validation: Validation
  hasAutoIncrementDefault: boolean
} {
  const validate = (value: Value, opts: { isRequired: boolean }) => {
    return validate_(
      value,
      config.fieldMeta.validation,
      opts.isRequired,
      config.label,
      config.fieldMeta.defaultValue === 'autoincrement'
    )
  }

  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    validation: config.fieldMeta.validation,
    defaultValue: {
      kind: 'create',
      value:
        config.fieldMeta.defaultValue === 'autoincrement' ? null : config.fieldMeta.defaultValue,
    },
    deserialize: data => ({ kind: 'update', value: data[config.path], initial: data[config.path] }),
    serialize: value => ({ [config.path]: value.value }),
    hasAutoIncrementDefault: config.fieldMeta.defaultValue === 'autoincrement',
    validate: (value, opts) => validate(value, opts) === undefined,
    filter: {
      Filter(props) {
        const {
          autoFocus,
          context,
          forceValidation,
          typeLabel,
          onChange,
          type,
          value,
          ...otherProps
        } = props
        const [isDirty, setDirty] = useState(false)
        if (type === 'empty' || type === 'not_empty') return null

        const labelProps =
          context === 'add' ? { label: config.label, description: typeLabel } : { label: typeLabel }

        return (
          <NumberField
            {...otherProps}
            {...labelProps}
            autoFocus={autoFocus}
            errorMessage={
              (forceValidation || isDirty) &&
              !validate({ kind: 'update', initial: null, value }, { isRequired: true })
                ? 'Required'
                : null
            }
            step={1}
            width="auto"
            onBlur={() => setDirty(true)}
            onChange={x => onChange?.(!Number.isFinite(x) ? null : x)}
            value={value ?? NaN}
          />
        )
      },

      graphql: ({ type, value }) => {
        if (type === 'empty') return { [config.path]: { equals: null } }
        if (type === 'not_empty') return { [config.path]: { not: { equals: null } } }
        if (type === 'not') return { [config.path]: { not: { equals: value } } }
        return { [config.path]: { [type]: value } }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (type === 'equals' && value === null) {
            return [{ type: 'empty', value: null }]
          }
          if (!value) return []
          if (type === 'equals') return { type: 'equals', value }
          if (type === 'not') {
            if (value?.equals === null) return { type: 'not_empty', value: null }
            if (value?.equals === undefined) return []
            return { type: 'not', value: value.equals }
          }
          if (type === 'gt' || type === 'gte' || type === 'lt' || type === 'lte') {
            return { type, value }
          }
          return []
        })
      },
      Label({ label, type, value }) {
        if (type === 'empty' || type === 'not_empty') return label.toLocaleLowerCase()
        const operator = TYPE_OPERATOR_MAP[type as keyof typeof TYPE_OPERATOR_MAP]
        return `${operator} ${value}`
      },
      types: {
        equals: {
          label: 'Is exactly',
          initialValue: null,
        },
        not: {
          label: 'Is not exactly',
          initialValue: null,
        },
        gt: {
          label: 'Is greater than',
          initialValue: null,
        },
        lt: {
          label: 'Is less than',
          initialValue: null,
        },
        gte: {
          label: 'Is greater than or equal to',
          initialValue: null,
        },
        lte: {
          label: 'Is less than or equal to',
          initialValue: null,
        },
        empty: {
          label: 'Is empty',
          initialValue: null,
        },
        not_empty: {
          label: 'Is not empty',
          initialValue: null,
        },
      },
    },
  }
}

export function Field({
  field,
  value,
  onChange,
  autoFocus,
  forceValidation,
  isRequired,
}: FieldProps<typeof controller>) {
  const [isDirty, setDirty] = useState(false)
  const isReadOnly = !onChange || field.hasAutoIncrementDefault

  if (field.hasAutoIncrementDefault && value.kind === 'create') {
    return (
      <NumberField
        autoFocus={autoFocus}
        description={field.description}
        label={field.label}
        isReadOnly
        contextualHelp={
          <ContextualHelp>
            <Heading>Auto increment</Heading>
            <Content>
              <Text>
                This field is set to auto increment. It will default to the next available number.
              </Text>
            </Content>
          </ContextualHelp>
        }
      />
    )
  }

  const validate = (value: Value) => {
    return validate_(
      value,
      field.validation,
      isRequired,
      field.label,
      field.hasAutoIncrementDefault
    )
  }

  return (
    <NumberField
      autoFocus={autoFocus}
      description={field.description}
      label={field.label}
      errorMessage={(forceValidation || isDirty) && validate(value)}
      isReadOnly={isReadOnly}
      isRequired={isRequired}
      width="alias.singleLineWidth"
      onBlur={() => setDirty(true)}
      onChange={x => onChange?.({ ...value, value: !Number.isFinite(x) ? null : x })}
      value={value.value ?? NaN}
    />
  )
}
</file>

<file path="packages/core/src/fields/types/integer/index.ts">
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { filters } from '../../filters'
import { resolveDbNullable, makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import type { controller } from './views'

export type IntegerFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'Int'>
> & {
  isIndexed?: boolean | 'unique'
  defaultValue?: number | null | { kind: 'autoincrement' }
  validation?: {
    isRequired?: boolean
    min?: number
    max?: number
  }
  db?: {
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
  }
}

// for a signed 32-bit integer
const MAX_INT = 0x7fffffff
const MIN_INT = -0x80000000

export function integer<ListTypeInfo extends BaseListTypeInfo>(
  config: IntegerFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { defaultValue: defaultValue_, isIndexed, validation = {} } = config

  const { isRequired = false, min, max } = validation
  const defaultValue =
    typeof defaultValue_ === 'number' ? defaultValue_ : (defaultValue_?.kind ?? null)

  return meta => {
    if (defaultValue === 'autoincrement') {
      if (meta.provider === 'sqlite' || meta.provider === 'mysql') {
        throw new Error(
          `${meta.listKey}.${meta.fieldKey} specifies defaultValue: { kind: 'autoincrement' }, this is not supported on ${meta.provider}`
        )
      }
      const isNullable = resolveDbNullable(validation, config.db)
      if (isNullable !== false) {
        throw new Error(
          `${meta.listKey}.${meta.fieldKey} specifies defaultValue: { kind: 'autoincrement' } but doesn't specify db.isNullable: false.\n` +
            `Having nullable autoincrements on Prisma currently incorrectly creates a non-nullable column so it is not allowed.\n` +
            `https://github.com/prisma/prisma/issues/8663`
        )
      }
    }
    if (defaultValue !== null && !Number.isInteger(defaultValue)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a default value of: ${defaultValue} but it must be a valid finite number`
      )
    }
    if (min !== undefined && !Number.isInteger(min)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.min: ${min} but it must be an integer`
      )
    }
    if (max !== undefined && !Number.isInteger(max)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.max: ${max} but it must be an integer`
      )
    }
    if (min !== undefined && (min > MAX_INT || min < MIN_INT)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.min: ${min} which is outside of the range of a 32-bit signed integer`
      )
    }
    if (max !== undefined && (max > MAX_INT || max < MIN_INT)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.max: ${max} which is outside of the range of a 32-bit signed integer`
      )
    }
    if (min !== undefined && max !== undefined && min > max) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a validation.max that is less than the validation.min, and therefore has no valid options`
      )
    }

    const hasAdditionalValidation = min !== undefined || max !== undefined
    const { mode, validate } = makeValidateHook(
      meta,
      config,
      hasAdditionalValidation
        ? ({ resolvedData, operation, addValidationError }) => {
            if (operation === 'delete') return

            const value = resolvedData[meta.fieldKey]
            if (typeof value === 'number') {
              if (min !== undefined && value < min) {
                addValidationError(`value must be greater than or equal to ${min}`)
              }

              if (max !== undefined && value > max) {
                addValidationError(`value must be less than or equal to ${max}`)
              }
            }
          }
        : undefined
    )

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: 'Int',
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      default:
        typeof defaultValue === 'number'
          ? { kind: 'literal', value: defaultValue }
          : defaultValue === 'autoincrement'
            ? { kind: 'autoincrement' }
            : undefined,
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, isRequired),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.Int }) } : undefined,
        where: {
          arg: g.arg({ type: filters[meta.provider].Int[mode] }),
          resolve: mode === 'optional' ? filters.resolveCommon : undefined,
        },
        create: {
          arg: g.arg({
            type: g.Int,
            defaultValue: typeof defaultValue === 'number' ? defaultValue : undefined,
          }),
          resolve(value) {
            if (value === undefined) {
              if (defaultValue === 'autoincrement') return null
              return defaultValue
            }
            return value
          },
        },
        update: { arg: g.arg({ type: g.Int }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({ type: g.Int }),
      __ksTelemetryFieldTypeName: '@keystone-6/integer',
      views: '@keystone-6/core/fields/types/integer/views',
      getAdminMeta(): Parameters<typeof controller>[0]['fieldMeta'] {
        return {
          validation: {
            min: min ?? MIN_INT,
            max: max ?? MAX_INT,
          },
          defaultValue,
        }
      },
    })
  }
}
</file>

<file path="packages/core/src/fields/types/json/views/index.tsx">
import { css, tokenSchema } from '@keystar/ui/style'
import { TextArea } from '@keystar/ui/text-field'
import { Text } from '@keystar/ui/typography'

import {
  type CellComponent,
  type FieldController,
  type FieldControllerConfig,
  type FieldProps,
  type JSONValue,
} from '../../../../types'

export const Field = (props: FieldProps<typeof controller>) => {
  const { autoFocus, field, forceValidation, onChange, value } = props
  const errorMessage = forceValidation ? 'Invalid JSON' : undefined

  return (
    <TextArea
      autoFocus={autoFocus}
      description={field.description}
      errorMessage={errorMessage}
      isReadOnly={onChange === undefined}
      label={field.label}
      onChange={onChange}
      value={value}
      UNSAFE_className={css({
        textarea: {
          fontSize: tokenSchema.typography.text.small.size,
          fontFamily: tokenSchema.typography.fontFamily.code,
        },
      })}
    />
  )
}

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  return value ? <Text>{JSON.stringify(value)}</Text> : null
}

type Config = FieldControllerConfig<{ defaultValue: JSONValue }>

export function controller(config: Config): FieldController<string, string> {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    defaultValue:
      config.fieldMeta.defaultValue === null
        ? ''
        : JSON.stringify(config.fieldMeta.defaultValue, null, 2),
    validate: value => {
      if (!value) return true
      try {
        JSON.parse(value)
        return true
      } catch (e) {
        return false
      }
    },
    deserialize: data => {
      const value = data[config.path]
      // null is equivalent to Prisma.DbNull, and we show that as an empty input
      if (value === null) return ''
      return JSON.stringify(value, null, 2)
    },
    serialize: value => {
      if (!value) return { [config.path]: null }
      try {
        return { [config.path]: JSON.parse(value) }
      } catch (e) {
        return { [config.path]: undefined }
      }
    },
  }
}
</file>

<file path="packages/core/src/fields/types/json/index.ts">
import {
  type BaseListTypeInfo,
  type JSONValue,
  type FieldTypeFunc,
  type CommonFieldConfig,
  jsonFieldTypePolyfilledForSQLite,
} from '../../../types'
import { g } from '../../..'
import type { controller } from './views'

type FieldTypeInfo = {
  item: JSONValue | null
  inputs: {
    where: never
    create: JSONValue | undefined
    update: JSONValue | undefined
    uniqueWhere: never
    orderBy: never
  }
  prisma: {
    create: JSONValue | undefined
    update: JSONValue | undefined
  }
}

export type JsonFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> & {
  defaultValue?: JSONValue
  db?: { map?: string; extendPrismaSchema?: (field: string) => string }
}

export const json =
  <ListTypeInfo extends BaseListTypeInfo>({
    defaultValue = null,
    ...config
  }: JsonFieldConfig<ListTypeInfo> = {}): FieldTypeFunc<ListTypeInfo> =>
  meta => {
    if ((config as any).isIndexed === 'unique') {
      throw Error("isIndexed: 'unique' is not a supported option for field type json")
    }

    return jsonFieldTypePolyfilledForSQLite(
      meta.provider,
      {
        ...config,
        __ksTelemetryFieldTypeName: '@keystone-6/json',
        input: {
          create: {
            arg: g.arg({ type: g.JSON }),
            resolve(val) {
              return val === undefined ? defaultValue : val
            },
          },
          update: { arg: g.arg({ type: g.JSON }) },
        },
        output: g.field({ type: g.JSON }),
        views: '@keystone-6/core/fields/types/json/views',
        getAdminMeta: (): Parameters<typeof controller>[0]['fieldMeta'] => ({
          defaultValue,
        }),
      },
      {
        default:
          defaultValue === null
            ? undefined
            : { kind: 'literal', value: JSON.stringify(defaultValue) },
        map: config.db?.map,
        extendPrismaSchema: config.db?.extendPrismaSchema,
      }
    )
  }
</file>

<file path="packages/core/src/fields/types/multiselect/views/index.tsx">
import { useState } from 'react'
import { useFilter, useListFormatter } from '@react-aria/i18n'

import { Checkbox, CheckboxGroup } from '@keystar/ui/checkbox'
import { Combobox, Item } from '@keystar/ui/combobox'
import { VStack } from '@keystar/ui/layout'
import { TagGroup } from '@keystar/ui/tag'
import { Text } from '@keystar/ui/typography'

import type {
  CellComponent,
  FieldController,
  FieldControllerConfig,
  FieldProps,
} from '../../../../types'

export function Field(props: FieldProps<typeof controller>) {
  if (props.field.displayMode === 'checkboxes') return <CheckboxesModeField {...props} />
  return <SelectModeField {...props} />
}

function SelectModeField(props: FieldProps<typeof controller>) {
  const { field, onChange, value } = props
  const [filterText, setFilterText] = useState('')
  const { contains } = useFilter({ sensitivity: 'base' })
  const items = field.options.filter(option => !value.some(x => x.value === option.value))
  const filteredItems = filterText ? items.filter(item => contains(item.label, filterText)) : items

  return (
    <VStack gap="regular">
      <Combobox
        label={field.label}
        description={field.description}
        isReadOnly={onChange === undefined}
        items={filteredItems}
        loadingState="idle"
        onInputChange={setFilterText}
        inputValue={filterText}
        // selectedKey={null}
        onSelectionChange={key => {
          if (key == null) return
          onChange?.([...value, field.valuesToOptionsWithStringValues[key]])
        }}
        width="auto"
      >
        {item => <Item key={item.value}>{item.label}</Item>}
      </Combobox>

      <TagGroup
        aria-label={`${field.label} selected items`}
        items={value}
        maxRows={2}
        onRemove={keys => {
          const key = keys.values().next().value
          onChange?.(value.filter(x => x.value !== key))
        }}
        renderEmptyState={() => (
          <Text color="neutralSecondary" size="small">
            No items…
          </Text>
        )}
      >
        {item => <Item key={item.value}>{item.label}</Item>}
      </TagGroup>
    </VStack>
  )
}

function CheckboxesModeField(props: FieldProps<typeof controller>) {
  const { field, onChange, value } = props
  return (
    <CheckboxGroup
      label={field.label}
      description={field.description}
      isReadOnly={onChange === undefined}
      value={value.map(x => x.value)}
      onChange={keys => {
        onChange?.(keys.map(key => field.valuesToOptionsWithStringValues[key]))
      }}
    >
      {field.options.map(option => (
        <Checkbox key={option.value} value={option.value}>
          {option.label}
        </Checkbox>
      ))}
    </CheckboxGroup>
  )
}

export const Cell: CellComponent<typeof controller> = ({ value = [], field }) => {
  const listFormatter = useListFormatter({ style: 'short', type: 'conjunction' })
  const labels = (value as string[]).map(x => field.valuesToOptionsWithStringValues[x].label)

  let cellContent = null
  if (value.length > 3) {
    cellContent = listFormatter.format([labels[0], `${value.length - 1} more`])
  } else {
    cellContent = listFormatter.format(labels)
  }

  return <Text>{cellContent}</Text>
}

export type AdminMultiSelectFieldMeta = {
  options: readonly { label: string; value: string | number }[]
  type: 'string' | 'integer' | 'enum'
  displayMode: 'checkboxes' | 'select'
  defaultValue: string[] | number[]
}

type Config = FieldControllerConfig<AdminMultiSelectFieldMeta>
type Option = { label: string; value: string }
type Value = readonly Option[]

export function controller(config: Config): FieldController<Value, Option[]> & {
  displayMode: 'checkboxes' | 'select'
  options: Option[]
  type: 'string' | 'integer' | 'enum'
  valuesToOptionsWithStringValues: Record<string, Option>
} {
  const optionsWithStringValues = config.fieldMeta.options.map(x => ({
    label: x.label,
    value: x.value.toString(),
  }))

  const valuesToOptionsWithStringValues = Object.fromEntries(
    optionsWithStringValues.map(option => [option.value, option])
  )
  const parseValue = (value: string) =>
    config.fieldMeta.type === 'integer' ? parseInt(value) : value

  return {
    displayMode: config.fieldMeta.displayMode,
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    defaultValue: config.fieldMeta.defaultValue.map(x => valuesToOptionsWithStringValues[x]),
    type: config.fieldMeta.type,
    options: optionsWithStringValues,
    valuesToOptionsWithStringValues,
    deserialize: data => {
      // if we get null from the GraphQL API (which will only happen if field read access control failed)
      // we'll just show it as nothing being selected for now.
      const values: readonly string[] | readonly number[] = data[config.path] ?? []
      const selectedOptions = values.map(x => valuesToOptionsWithStringValues[x])
      return selectedOptions
    },
    serialize: value => ({ [config.path]: value.map(x => parseValue(x.value)) }),
  }
}
</file>

<file path="packages/core/src/fields/types/multiselect/index.ts">
import { classify } from 'inflection'
import { humanize } from '../../../lib/utils'
import type { JSONValue } from '../../../types'
import {
  type BaseListTypeInfo,
  type FieldTypeFunc,
  type CommonFieldConfig,
  type FieldData,
  jsonFieldTypePolyfilledForSQLite,
} from '../../../types'
import { g } from '../../..'
import { makeValidateHook } from '../../non-null-graphql'
import type { controller } from './views'

type FieldTypeInfo = {
  item: JSONValue | null
  inputs: {
    where: never
    create: JSONValue | undefined
    update: JSONValue | undefined
    uniqueWhere: never
    orderBy: never
  }
  prisma: {
    create: JSONValue | undefined
    update: JSONValue | undefined
  }
}

export type MultiselectFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> &
  (
    | {
        /**
         * When a value is provided as just a string, it will be formatted in the same way
         * as field labels are to create the label.
         */
        options: readonly ({ label: string; value: string } | string)[]
        /**
         * If `enum` is provided on SQLite, it will use an enum in GraphQL but a string in the database.
         */
        type?: 'string' | 'enum'
        defaultValue?: readonly string[] | null
      }
    | {
        options: readonly { label: string; value: number }[]
        type: 'integer'
        defaultValue?: readonly number[] | null
      }
  ) & {
    ui?: {
      displayMode?: 'checkboxes' | 'select'
    }
    db?: {
      isNullable?: boolean
      map?: string
      extendPrismaSchema?: (field: string) => string
    }
  }

// these are the lowest and highest values for a signed 32-bit integer
const MAX_INT = 2147483647
const MIN_INT = -2147483648

export function multiselect<ListTypeInfo extends BaseListTypeInfo>(
  config: MultiselectFieldConfig<ListTypeInfo>
): FieldTypeFunc<ListTypeInfo> {
  const { defaultValue: defaultValue_, ui: { displayMode = 'select', ...ui } = {} } = config

  config.db ??= {}
  config.db.isNullable ??= false // TODO: deprecated, remove in breaking change
  const defaultValue = config.db.isNullable ? defaultValue_ : (defaultValue_ ?? []) // TODO: deprecated, remove in breaking change?

  return meta => {
    if ((config as any).isIndexed === 'unique') {
      throw TypeError("isIndexed: 'unique' is not a supported option for field type multiselect")
    }

    const resolveCreate = <T extends string | number>(val: T[] | null | undefined): T[] | null => {
      const resolved = resolveUpdate(val)
      if (resolved === undefined) {
        return defaultValue as T[]
      }
      return resolved
    }

    const resolveUpdate = <T extends string | number>(
      val: T[] | null | undefined
    ): T[] | null | undefined => {
      return val
    }

    const transformedConfig = configToOptionsAndGraphQLType(config, meta)

    const type = g.list(g.nonNull(transformedConfig.graphqlType))

    const accepted = new Set(transformedConfig.options.map(x => x.value))
    if (accepted.size !== transformedConfig.options.length) {
      throw new Error(`${meta.listKey}.${meta.fieldKey} has duplicate options, this is not allowed`)
    }

    const { mode, validate } = makeValidateHook(
      meta,
      config,
      ({ inputData, operation, addValidationError }) => {
        if (operation === 'delete') return

        const values: readonly (string | number)[] | null | undefined = inputData[meta.fieldKey] // resolvedData is JSON
        if (values != null) {
          for (const value of values) {
            if (!accepted.has(value)) {
              addValidationError(`'${value}' is not an accepted option`)
            }
          }
          if (new Set(values).size !== values.length) {
            addValidationError(`non-unique set of options selected`)
          }
        }
      }
    )

    return jsonFieldTypePolyfilledForSQLite(
      meta.provider,
      {
        ...config,
        ui,
        __ksTelemetryFieldTypeName: '@keystone-6/multiselect',
        hooks: {
          ...config.hooks,
          validate,
        },
        views: '@keystone-6/core/fields/types/multiselect/views',
        getAdminMeta: (): Parameters<typeof controller>[0]['fieldMeta'] => ({
          options: transformedConfig.options,
          type: config.type ?? 'string',
          displayMode: displayMode,
          defaultValue: [],
        }),
        input: {
          create: { arg: g.arg({ type }), resolve: resolveCreate },
          update: { arg: g.arg({ type }), resolve: resolveUpdate },
        },
        output: g.field({
          type: type,
          resolve({ value }) {
            return value as any
          },
        }),
      },
      {
        mode,
        map: config?.db?.map,
        extendPrismaSchema: config.db?.extendPrismaSchema,
        default: {
          kind: 'literal',
          value: JSON.stringify(defaultValue ?? null),
        },
      }
    )
  }
}

function configToOptionsAndGraphQLType(
  config: MultiselectFieldConfig<BaseListTypeInfo>,
  meta: FieldData
) {
  if (config.type === 'integer') {
    if (
      config.options.some(
        ({ value }) => !Number.isInteger(value) || value > MAX_INT || value < MIN_INT
      )
    ) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies integer values that are outside the range of a 32-bit signed integer`
      )
    }
    return {
      graphqlType: g.Int,
      options: config.options,
    }
  }

  const options = config.options.map(option => {
    if (typeof option === 'string') {
      return {
        label: humanize(option),
        value: option,
      }
    }
    return option
  })

  if (config.type === 'enum') {
    const enumName = `${meta.listKey}${classify(meta.fieldKey)}Type`
    const graphqlType = g.enum({
      name: enumName,
      values: g.enumValues(options.map(x => x.value)),
    })
    return {
      graphqlType,
      options,
    }
  }
  return {
    graphqlType: g.String,
    options,
  }
}
</file>

<file path="packages/core/src/fields/types/password/views/index.tsx">
// @ts-expect-error
import dumbPasswords from 'dumb-passwords'
import { useEffect, useId, useRef, useState } from 'react'
import { useSlotId } from '@react-aria/utils'

import { ActionButton, ToggleButton } from '@keystar/ui/button'
import { Checkbox } from '@keystar/ui/checkbox'
import { FieldLabel, FieldMessage } from '@keystar/ui/field'
import { Icon } from '@keystar/ui/icon'
import { eyeIcon } from '@keystar/ui/icon/icons/eyeIcon'
import { asteriskIcon } from '@keystar/ui/icon/icons/asteriskIcon'
import { Flex, VStack } from '@keystar/ui/layout'
import { containerQueries, css } from '@keystar/ui/style'
import { TextField } from '@keystar/ui/text-field'
import { Text, VisuallyHidden } from '@keystar/ui/typography'

import type {
  CellComponent,
  FieldController,
  FieldControllerConfig,
  FieldProps,
} from '../../../../types'

function validate(
  value: Value,
  validation: Validation,
  isRequired: boolean,
  fieldLabel: string
): string | undefined {
  if (value.kind === 'initial' && (value.isSet === null || value.isSet === true)) {
    return undefined
  }
  if (value.kind === 'initial' && isRequired) {
    return `${fieldLabel} is required`
  }
  if (value.kind === 'editing' && value.confirm !== value.value) {
    return `The passwords do not match`
  }
  if (value.kind === 'editing') {
    const val = value.value
    if (val.length < validation.length.min) {
      if (validation.length.min === 1) {
        return `${fieldLabel} must not be empty`
      }
      return `${fieldLabel} must be at least ${validation.length.min} characters long`
    }
    if (validation.length.max !== null && val.length > validation.length.max) {
      return `${fieldLabel} must be no longer than ${validation.length.max} characters`
    }
    if (validation.match && !validation.match.regex.test(val)) {
      return validation.match.explanation
    }
    if (validation.rejectCommon && dumbPasswords.check(val)) {
      return `${fieldLabel} is too common and is not allowed`
    }
  }
  return undefined
}

function readonlyCheckboxProps(isSet: null | undefined | boolean) {
  const isIndeterminate = isSet == null
  const isSelected = isSet == null ? undefined : isSet
  return {
    children: isIndeterminate ? 'Access denied' : 'Value is set',
    isIndeterminate,
    isReadOnly: true,
    isSelected,
    prominence: 'low' as const,
  }
}

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, forceValidation, onChange, value } = props

  const [secureTextEntry, setSecureTextEntry] = useState(true)
  const [touched, setTouched] = useState({ value: false, confirm: false })
  const triggerRef = useRef<HTMLButtonElement>(null)

  const isReadOnly = onChange == null
  const validationMessage =
    forceValidation || (touched.value && touched.confirm)
      ? validate(value, field.validation, props.isRequired, field.label)
      : undefined

  const labelId = useId()
  const descriptionId = useSlotId([!!field.description, !!validationMessage])
  const messageId = useSlotId([!!field.description, !!validationMessage])

  const cancelEditing = () => {
    onChange?.({ kind: 'initial', isSet: value.isSet })
    setTimeout(() => {
      triggerRef.current?.focus()
    }, 0)
  }
  const onEscape = (e: React.KeyboardEvent) => {
    if (e.key !== 'Escape' || value.kind !== 'editing') return
    if (value.value === '' && value.confirm === '') {
      cancelEditing()
    }
  }

  // reset when the user cancels, or when the form is submitted
  useEffect(() => {
    if (value.kind === 'initial') {
      setTouched({ value: false, confirm: false })
      setSecureTextEntry(true)
    }
  }, [value.kind])

  return (
    <VStack
      role="group"
      aria-labelledby={labelId}
      aria-describedby={descriptionId}
      gap="medium"
      minWidth={0}
    >
      <FieldLabel elementType="span" id={labelId}>
        {field.label}
      </FieldLabel>
      {!!field.description && (
        <Text id={descriptionId} size="regular" color="neutralSecondary">
          {field.description}
        </Text>
      )}
      {isReadOnly ? (
        <Checkbox {...readonlyCheckboxProps(value.isSet)} />
      ) : value.kind === 'initial' ? (
        <ActionButton
          ref={triggerRef}
          alignSelf="start"
          autoFocus={autoFocus}
          onPress={() => {
            onChange({
              kind: 'editing',
              confirm: '',
              value: '',
              isSet: value.isSet,
            })
          }}
        >
          {value.isSet ? `Change ` : `Set `}
          {field.label.toLocaleLowerCase()}
        </ActionButton>
      ) : (
        <Flex
          gap="regular"
          UNSAFE_className={css({
            [containerQueries.below.tablet]: {
              flexDirection: 'column',
            },
          })}
        >
          <TextField
            autoFocus
            aria-label={`new ${field.label}`}
            aria-describedby={[descriptionId, messageId].filter(Boolean).join(' ')}
            // @ts-expect-error — needs to be fixed in "@keystar/ui"
            isInvalid={!!validationMessage}
            onBlur={() => setTouched({ ...touched, value: true })}
            onChange={text => onChange({ ...value, value: text })}
            onKeyDown={onEscape}
            placeholder="New"
            type={secureTextEntry ? 'password' : 'text'}
            value={value.value}
            flex
          />
          <TextField
            aria-label={`confirm ${field.label}`}
            aria-describedby={messageId} // don't repeat the description announcement for the confirm field
            // @ts-expect-error — needs to be fixed in "@keystar/ui"
            isInvalid={!!validationMessage}
            onBlur={() => setTouched({ ...touched, confirm: true })}
            onChange={text => onChange({ ...value, confirm: text })}
            onKeyDown={onEscape}
            placeholder="Confirm"
            type={secureTextEntry ? 'password' : 'text'}
            value={value.confirm}
            flex
          />

          <Flex gap="regular">
            <ToggleButton
              aria-label="show"
              isSelected={!secureTextEntry}
              onPress={() => setSecureTextEntry(bool => !bool)}
            >
              <Icon src={eyeIcon} />
              <Text
                UNSAFE_className={css({
                  [containerQueries.above.mobile]: {
                    display: 'none',
                  },
                })}
              >
                Show
              </Text>
            </ToggleButton>
            <ActionButton onPress={cancelEditing}>Cancel</ActionButton>
          </Flex>
        </Flex>
      )}
      {!!validationMessage && <FieldMessage id={messageId}>{validationMessage}</FieldMessage>}
    </VStack>
  )
}

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  return value !== null ? (
    <div aria-label="is set" style={{ display: 'flex' }}>
      <Icon src={asteriskIcon} size="small" />
      <Icon src={asteriskIcon} size="small" />
      <Icon src={asteriskIcon} size="small" />
    </div>
  ) : (
    <VisuallyHidden>not set</VisuallyHidden>
  )
}

type Validation = {
  rejectCommon: boolean
  match: {
    regex: RegExp
    explanation: string
  } | null
  length: {
    min: number
    max: number | null
  }
}

export type PasswordFieldMeta = {
  isNullable: boolean
  validation: {
    rejectCommon: boolean
    match: {
      regex: { source: string; flags: string }
      explanation: string
    } | null
    length: {
      min: number
      max: number | null
    }
  }
}

type Value =
  | {
      kind: 'initial'
      isSet: boolean | null
    }
  | {
      kind: 'editing'
      isSet: boolean | null
      value: string
      confirm: string
    }

export function controller(config: FieldControllerConfig<PasswordFieldMeta>): FieldController<
  Value,
  boolean | null,
  { isSet?: boolean | null | undefined }
> & {
  validation: Validation
} {
  const validation: Validation = {
    ...config.fieldMeta.validation,
    match:
      config.fieldMeta.validation.match === null
        ? null
        : {
            regex: new RegExp(
              config.fieldMeta.validation.match.regex.source,
              config.fieldMeta.validation.match.regex.flags
            ),
            explanation: config.fieldMeta.validation.match.explanation,
          },
  }
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: `${config.path} {isSet}`,
    validation,
    defaultValue: {
      kind: 'initial',
      isSet: false,
    },
    validate: (state, opts) =>
      validate(state, validation, opts.isRequired, config.label) === undefined,
    deserialize: data => ({ kind: 'initial', isSet: data[config.path]?.isSet ?? null }),
    serialize: value => {
      if (value.kind === 'initial') return {}
      return { [config.path]: value.value }
    },
    filter:
      config.fieldMeta.isNullable === false
        ? undefined
        : {
            Filter(props) {
              const { autoFocus, context, typeLabel, onChange, value, type, ...otherProps } = props
              return (
                <Checkbox
                  autoFocus={autoFocus}
                  onChange={onChange}
                  isSelected={value ?? false}
                  {...otherProps}
                >
                  {typeLabel} set
                </Checkbox>
              )
            },
            graphql({ type, value }) {
              return {
                [config.path]: {
                  isSet: type === 'not' ? !value : value,
                },
              }
            },
            parseGraphQL: value => {
              if (value?.isSet !== undefined) {
                return [{ type: 'is', value: value.isSet }]
              }
              return []
            },
            Label({ type, value }) {
              if ((type === 'is' && value) || (type === 'not' && !value)) return `is set`
              return `is not set`
            },
            types: {
              is: {
                label: 'Is',
                initialValue: true,
              },
              not: {
                label: 'Is not',
                initialValue: true,
              },
            },
          },
  }
}
</file>

<file path="packages/core/src/fields/types/password/index.ts">
import bcryptjs from 'bcryptjs'
// @ts-expect-error
import dumbPasswords from 'dumb-passwords'
import { userInputError } from '../../../lib/core/graphql-errors'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
} from '../../../types'
import { g } from '../../..'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import { isObjectType, type GraphQLSchema } from 'graphql'
import type { InferValueFromInputType } from '@graphql-ts/schema'
import type { controller } from './views'

type FieldTypeInfo = {
  item: string | null
  inputs: {
    where: InferValueFromInputType<typeof PasswordFilter> | undefined
    create: string | null | undefined
    update: string | null | undefined
    uniqueWhere: never
    orderBy: never
  }
  prisma: {
    create: string | null | undefined
    update: string | null | undefined
  }
}

export type PasswordFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> & {
  validation?: {
    isRequired?: boolean
    rejectCommon?: boolean
    match?: { regex: RegExp; explanation?: string }
    length?: {
      /** @default 8 */
      min?: number
      max?: number
    }
  }
  db?: {
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
  }
  kdf?: KDF
}

type KDF = {
  compare(preImage: string, hash: string): Promise<boolean>
  hash(preImage: string): Promise<string>
}

const PasswordState = g.object<{ isSet: boolean }>()({
  name: 'PasswordState',
  fields: {
    isSet: g.field({ type: g.nonNull(g.Boolean) }),
  },
})

const PasswordFilter = g.inputObject({
  name: 'PasswordFilter',
  fields: {
    isSet: g.arg({ type: g.nonNull(g.Boolean) }),
  },
})

export function password<ListTypeInfo extends BaseListTypeInfo>(
  config: PasswordFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const {
    kdf = {
      hash: secret => {
        // note this is slightly different to checking .length > 72 because
        // bcrypt will truncate to 72 bytes after utf8 encoding
        // not JS string length which may be different since that's the utf16 length
        // (though using characters in the error message makes sense since users shouldn't have to think about bytes and it aligns with the validation messages below)
        if (bcryptjs.truncates(secret))
          throw new Error('value must be no longer than 72 characters')
        return bcryptjs.hash(secret, 10)
      },
      compare: (secret, hash) => bcryptjs.compare(secret, hash),
    },
    validation = {},
  } = config
  const { isRequired = false, rejectCommon = false, match, length: { max } = {} } = validation
  const min = isRequired ? (validation.length?.min ?? 8) : validation.length?.min

  return meta => {
    if ((config as any).isIndexed === 'unique') {
      throw Error("isIndexed: 'unique' is not a supported option for field type password")
    }
    if (min !== undefined && (!Number.isInteger(min) || min < 0)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.length.min: ${min} but it must be a positive integer`
      )
    }
    if (max !== undefined && (!Number.isInteger(max) || max < 0)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.length.max: ${max} but it must be a positive integer`
      )
    }
    if (isRequired && min !== undefined && min === 0) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.isRequired: true and validation.length.min: 0, this is not allowed because validation.isRequired implies at least a min length of 1`
      )
    }
    if (isRequired && max !== undefined && max === 0) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.isRequired: true and validation.length.max: 0, this is not allowed because validation.isRequired implies at least a max length of 1`
      )
    }
    if (min !== undefined && max !== undefined && min > max) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a validation.length.max that is less than the validation.length.min, and therefore has no valid options`
      )
    }

    function inputResolver(val: string | null | undefined) {
      if (val == null) return val
      return kdf.hash(val)
    }

    const hasAdditionalValidation = match || rejectCommon || min !== undefined || max !== undefined
    const { mode, validate } = makeValidateHook(
      meta,
      config,
      hasAdditionalValidation
        ? ({ inputData, operation, addValidationError }) => {
            if (operation === 'delete') return

            const value = inputData[meta.fieldKey] // we use inputData, as resolveData is hashed
            if (value != null) {
              if (min !== undefined && value.length < min) {
                if (min === 1) {
                  addValidationError(`value must not be empty`)
                } else {
                  addValidationError(`value must be at least ${min} characters long`)
                }
              }
              if (max !== undefined && value.length > max) {
                addValidationError(`value must be no longer than ${max} characters`)
              }
              if (match && !match.regex.test(value)) {
                addValidationError(match.explanation ?? `value must match ${match.regex}`)
              }
              if (rejectCommon && dumbPasswords.check(value)) {
                addValidationError(`value is too common and is not allowed`)
              }
            }
          }
        : undefined
    )

    return fieldType({
      kind: 'scalar',
      scalar: 'String',
      mode,
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, isRequired),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        where:
          mode === 'required'
            ? undefined
            : {
                arg: g.arg({ type: PasswordFilter }),
                resolve(val) {
                  if (val === null) throw userInputError('Password filters cannot be set to null')
                  if (val.isSet) return { not: null }
                  return null
                },
              },
        create: {
          arg: g.arg({ type: g.String }),
          resolve(val) {
            if (val === undefined) return null
            return inputResolver(val)
          },
        },
        update: {
          arg: g.arg({ type: g.String }),
          resolve: inputResolver,
        },
      },
      __ksTelemetryFieldTypeName: '@keystone-6/password',
      views: '@keystone-6/core/fields/types/password/views',
      getAdminMeta: (): Parameters<typeof controller>[0]['fieldMeta'] => ({
        isNullable: mode === 'optional',
        validation: {
          rejectCommon,
          match: match
            ? {
                regex: {
                  source: match.regex.source,
                  flags: match.regex.flags,
                },
                explanation: match.explanation ?? `value must match ${match.regex}`,
              }
            : null,
          length: {
            max: max ?? null,
            min: min ?? 8,
          },
        },
      }),
      output: g.field({
        type: PasswordState,
        resolve(val) {
          return { isSet: val.value !== null }
        },
        extensions: {
          keystoneKDF: kdf,
        },
      }),
    })
  }
}

export function getPasswordFieldKDF(
  schema: GraphQLSchema,
  listKey: string,
  fieldKey: string
): KDF | null {
  const gqlOutputType = schema.getType(listKey)
  if (!isObjectType(gqlOutputType)) return null
  const passwordField = gqlOutputType.getFields()[fieldKey]
  if (!passwordField?.extensions.keystoneKDF) return null
  return passwordField.extensions.keystoneKDF as KDF
}
</file>

<file path="packages/core/src/fields/types/relationship/views/ComboboxMany.tsx">
import { ComboboxMulti, Item } from '@keystar/ui/combobox'
import { css } from '@keystar/ui/style'

import type { ListMeta } from '../../../../types'
import type { RelationshipValue } from './types'
import { useApolloQuery } from './useApolloQuery'
import { useState } from 'react'

export function ComboboxMany({
  extraSelection = '',
  forceValidation,
  isDisabled,
  isLoading,
  isReadOnly,
  isRequired,
  labelField,
  list,
  searchFields,
  state,
  ...props
}: {
  autoFocus?: boolean
  description?: string
  forceValidation?: boolean
  isDisabled?: boolean
  isLoading?: boolean
  isReadOnly?: boolean
  isRequired?: boolean
  label?: string
  'aria-label'?: string
  labelField: string
  searchFields: string[]
  list: ListMeta
  placeholder?: string
  state: {
    kind: 'many'
    value: RelationshipValue[]
    onChange(value: RelationshipValue[]): void
  }
  extraSelection?: string
}) {
  const { data, loadingState, error, onLoadMore, search, setSearch } = useApolloQuery({
    labelField,
    list,
    searchFields,
    state,
  })
  const [shouldShowErrors, setShouldShowErrors] = useState(false)
  const validationMessages =
    isRequired && state.value.length === 0 ? [`At least one ${list.singular} is required`] : []

  // TODO: better error UI
  // TODO: Handle permission errors
  // (ie; user has permission to read this relationship field, but
  // not the related list, or some items on the list)
  if (error) return <span>Error</span>

  const items: RelationshipValue[] = data?.items?.map(x => ({ ...x, built: false })) ?? []
  const fetchedIds = new Set(items.map(item => item.id))

  for (const item of state.value) {
    if (!fetchedIds.has(item.id)) {
      items.push(item)
    }
  }

  return (
    <ComboboxMulti
      {...props}
      isDisabled={isDisabled || isReadOnly}
      isRequired={isRequired}
      items={items}
      loadingState={loadingState}
      errorMessage={
        !!validationMessages.length && (shouldShowErrors || forceValidation)
          ? validationMessages.join('. ')
          : undefined
      }
      onBlur={() => {
        setShouldShowErrors(true)
      }}
      onInputChange={setSearch}
      inputValue={search}
      onLoadMore={onLoadMore}
      selectedKeys={state.value?.map(item => item.id.toString())}
      onSelectionChange={selection => {
        // TODO
        if (selection === 'all') return

        const selectedItems = items.filter(item => selection.has(item.id.toString()))
        state.onChange(selectedItems)
      }}
      minWidth="alias.singleLineWidth"
      width="auto"
      UNSAFE_className={css({
        // This should probably be addressed in @keystar/ui/combobox
        // - the mobile variant should respect the `width` prop
        '[role="button"]': { width: 'auto' },
      })}
    >
      {item => <Item>{item.label || item.id}</Item>}
    </ComboboxMulti>
  )
}
</file>

<file path="packages/core/src/fields/types/relationship/views/ComboboxSingle.tsx">
import { useState } from 'react'

import { Combobox, Item } from '@keystar/ui/combobox'
import { css } from '@keystar/ui/style'

import type { ListMeta } from '../../../../types'
import type { RelationshipValue } from './types'
import { useApolloQuery } from './useApolloQuery'

export function ComboboxSingle({
  forceValidation,
  isLoading,
  isRequired,
  labelField,
  list,
  searchFields,
  state,
  ...props
}: {
  autoFocus?: boolean
  description?: string
  forceValidation?: boolean
  isDisabled?: boolean
  isLoading?: boolean
  isReadOnly?: boolean
  isRequired?: boolean
  label?: string
  labelField: string
  searchFields: string[]
  list: ListMeta
  placeholder?: string
  state: {
    kind: 'one'
    value: RelationshipValue | null
    onChange(value: RelationshipValue | null): void
  }
}) {
  const { data, loading, error, onLoadMore, search, setSearch } = useApolloQuery({
    labelField,
    list,
    searchFields,
    state,
  })

  const [shouldShowErrors, setShouldShowErrors] = useState(false)
  const validationMessages =
    isRequired && state.value === null ? [`A ${list.singular} is required`] : []
  const [lastSeenStateValue, setLastSeenStateValue] = useState(state.value)

  if (state.value !== lastSeenStateValue) {
    setLastSeenStateValue(state.value)
    setSearch(state.value?.label ?? '')
  }

  // TODO: better error UI
  // TODO: Handle permission errors
  // (ie; user has permission to read this relationship field, but
  // not the related list, or some items on the list)
  if (error) return <span>Error</span>

  const items: RelationshipValue[] = data?.items?.map(x => ({ ...x, built: false })) ?? []

  if (
    state.value !== null &&
    (state.value.built || !items.some(item => item.id === state.value?.id))
  ) {
    items.push(state.value)
  }

  return (
    <Combobox
      {...props}
      isRequired={isRequired}
      items={data?.items ?? []}
      loadingState={loading || isLoading ? 'loading' : 'idle'}
      errorMessage={
        !!validationMessages.length && (shouldShowErrors || forceValidation)
          ? validationMessages.join('. ')
          : undefined
      }
      onBlur={() => {
        setShouldShowErrors(true)
      }}
      onInputChange={input => {
        setSearch(input)

        // unset the selected value when the user clears the input
        if (input === '') state.onChange(null)
      }}
      inputValue={search}
      onLoadMore={onLoadMore}
      selectedKey={state.value ? state.value.id.toString() : null}
      onSelectionChange={key => {
        const selectedItem = items.find(item => item.id.toString() === key) ?? null
        state.onChange(selectedItem)
        setSearch(selectedItem?.label ?? '')
      }}
      minWidth="alias.singleLineWidth"
      width="auto"
      UNSAFE_className={css({
        // This should probably be addressed in @keystar/ui/combobox
        // - the mobile variant should respect the `width` prop
        '[role="button"]': { width: 'auto' },
      })}
    >
      {item => <Item>{item.label || item.id}</Item>}
    </Combobox>
  )
}
</file>

<file path="packages/core/src/fields/types/relationship/views/ContextualActions.tsx">
import { type Key, type PropsWithChildren, useMemo } from 'react'

import { Icon } from '@keystar/ui/icon'
import { arrowUpRightIcon } from '@keystar/ui/icon/icons/arrowUpRightIcon'
import { plusIcon } from '@keystar/ui/icon/icons/plusIcon'
import { Grid } from '@keystar/ui/layout'
import { ActionMenu, Item } from '@keystar/ui/menu'
import { Text } from '@keystar/ui/typography'

import { useList } from '../../../../admin-ui/context'
import type { FieldProps, ListMeta } from '../../../../types'
import type { RelationshipController } from './types'
import { ActionButton } from '@keystar/ui/button'
import { Tooltip, TooltipTrigger } from '@keystar/ui/tooltip'

type RelationshipProps = {
  onAdd: () => void
} & FieldProps<() => RelationshipController>

export function ContextualActions(props: PropsWithChildren<RelationshipProps>) {
  const { children, ...otherProps } = props
  return (
    <Grid gap="regular" alignItems="end" columns="minmax(0, 1fr) auto">
      {children}
      <ContextualActionsMenu {...otherProps} />
    </Grid>
  )
}

function ContextualActionsMenu(props: RelationshipProps) {
  const { field, onAdd, onChange } = props

  const foreignList = useList(field.refListKey)
  const relatedItemHref = useRelatedItemHref(props)
  const relatedItemLabel = useRelatedItemLabel(field)
  const allowAdd = !field.hideCreate && !!onChange
  const items = useMemo(() => {
    const result = []
    if (allowAdd) {
      result.push({
        icon: plusIcon,
        key: 'add',
        label: `Add ${foreignList.singular.toLocaleLowerCase()}`,
      })
    }

    result.push({
      key: 'view',
      icon: arrowUpRightIcon,
      href: relatedItemHref,
      label: relatedItemLabel,
    })

    return result
  }, [allowAdd, foreignList, relatedItemHref, relatedItemLabel])

  const onAction = (key: Key) => {
    switch (key) {
      case 'add': {
        onAdd()
        break
      }
    }
  }

  // we don't want to change the presence or lack thereof of a selected value
  // but since `allowAdd` is based on config, it's fairly static and showing
  // a menu when the menu will only have one item is quite silly
  if (!allowAdd) {
    return (
      <TooltipTrigger>
        <ActionButton {...(relatedItemHref ? { href: relatedItemHref } : { isDisabled: true })}>
          <Icon src={arrowUpRightIcon} />
        </ActionButton>
        <Tooltip>{relatedItemLabel}</Tooltip>
      </TooltipTrigger>
    )
  }

  return (
    <ActionMenu
      aria-label={`Actions for ${field.label}`}
      direction="bottom"
      align="end"
      isDisabled={items.length === 0}
      disabledKeys={relatedItemHref === null ? ['view'] : []}
      items={items}
      onAction={onAction}
    >
      {item => (
        <Item key={item.key} href={item.href ?? undefined} textValue={item.label}>
          <Icon src={item.icon} />
          <Text>{item.label}</Text>
        </Item>
      )}
    </ActionMenu>
  )
}

export function useRelatedItemLabel(field: RelationshipController) {
  const foreignList = useList(field.refListKey)
  if (field.many) {
    return `View related ${foreignList.plural.toLocaleLowerCase()}`
  }
  return `View ${foreignList.singular.toLocaleLowerCase()}`
}

export function useRelatedItemHref({
  field,
  value,
}: Pick<FieldProps<() => RelationshipController>, 'field' | 'value'>) {
  const foreignList = useList(field.refListKey)
  if (value.kind === 'one') {
    if (!value.value) return null
    // the related item isn't actually created yet so we can't view it
    if (value.value.built) return null

    return `/${foreignList.path}/${value.value.id}`
  }
  let query: string | undefined
  if (field.refFieldKey && value.id !== null) {
    query = buildQueryForRelationshipFieldWithForeignField(foreignList, field.refFieldKey, value.id)
  } else if (value.kind === 'many' && value.value.length > 0) {
    query = `!id_in=${JSON.stringify(value.value.map(x => x.id))}`
  }
  if (query === undefined) return null

  return `/${foreignList.path}?${query}`
}

export function buildQueryForRelationshipFieldWithForeignField(
  foreignList: ListMeta,
  refFieldKey: string,
  localId: string
) {
  const foreignField = foreignList.fields[refFieldKey]
  const foreignMany: boolean = (foreignField.fieldMeta as any).many
  return `!${refFieldKey}_${foreignMany ? 'some' : 'is'}=${JSON.stringify(foreignMany ? [localId] : localId)}`
}
</file>

<file path="packages/core/src/fields/types/relationship/views/index.tsx">
import { useListFormatter } from '@react-aria/i18n'
import { Fragment, useState } from 'react'

import { DialogContainer } from '@keystar/ui/dialog'
import { HStack, VStack } from '@keystar/ui/layout'
import { TextLink } from '@keystar/ui/link'
import { Item, TagGroup } from '@keystar/ui/tag'
import { TextField } from '@keystar/ui/text-field'
import { Numeral, Text } from '@keystar/ui/typography'

import { BuildItemDialog } from '../../../../admin-ui/components'
import { useList } from '../../../../admin-ui/context'
import type { CellComponent, FieldControllerConfig, FieldProps } from '../../../../types'

import { ActionButton } from '@keystar/ui/button'
import { Icon } from '@keystar/ui/icon'
import { arrowUpRightIcon } from '@keystar/ui/icon/icons/arrowUpRightIcon'
import { ComboboxMany } from './ComboboxMany'
import { ComboboxSingle } from './ComboboxSingle'
import {
  buildQueryForRelationshipFieldWithForeignField,
  ContextualActions,
} from './ContextualActions'
import { RelationshipTable } from './RelationshipTable'
import type { RelationshipController, RelationshipValue } from './types'

export { ComboboxMany, ComboboxSingle }

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, forceValidation = false, onChange, value, isRequired } = props
  const foreignList = useList(field.refListKey)
  const [dialogIsOpen, setDialogOpen] = useState(false)
  const description = field.description || undefined
  const isReadOnly = onChange === undefined
  const [counter, setCounter] = useState(1)

  if (value.kind === 'count') {
    if (field.display === 'table') {
      return <RelationshipTable field={field} value={value} />
    }
    const textField = (
      <TextField
        autoFocus={autoFocus}
        label={field.label}
        description={description}
        isReadOnly
        value={value.count.toString()}
        width="alias.singleLineWidth"
      />
    )
    if (!field.refFieldKey) return textField
    return (
      <HStack gap="small" alignItems="end">
        {textField}
        <ActionButton
          href={`/${foreignList.path}?${buildQueryForRelationshipFieldWithForeignField(foreignList, field.refFieldKey, value.id)}`}
        >
          <Icon src={arrowUpRightIcon} />
        </ActionButton>
      </HStack>
    )
  }

  return (
    <Fragment>
      <VStack gap="medium">
        <ContextualActions onAdd={() => setDialogOpen(true)} {...props}>
          {value.kind === 'many' ? (
            <ComboboxMany
              autoFocus={autoFocus}
              label={field.label}
              description={description}
              forceValidation={forceValidation}
              isReadOnly={isReadOnly}
              isRequired={isRequired}
              labelField={field.refLabelField}
              searchFields={field.refSearchFields}
              list={foreignList}
              state={{
                kind: 'many',
                value: value.value,
                onChange(newItems) {
                  onChange?.({ ...value, value: newItems })
                },
              }}
            />
          ) : (
            <ComboboxSingle
              autoFocus={autoFocus}
              label={field.label}
              description={description}
              forceValidation={forceValidation}
              isReadOnly={isReadOnly}
              isRequired={isRequired}
              labelField={field.refLabelField}
              searchFields={field.refSearchFields}
              list={foreignList}
              state={{
                kind: 'one',
                value: value.value,
                onChange(newItem) {
                  onChange?.({ ...value, value: newItem })
                },
              }}
            />
          )}
        </ContextualActions>

        {value.kind === 'many' && (
          <TagGroup
            aria-label={`related ${foreignList.plural}`}
            isRequired={isRequired}
            items={value.value.map(item => ({
              id: item.id.toString() ?? '',
              label: item.label ?? '',
              href: item.built ? '' : `/${foreignList.path}/${item.id}`,
            }))}
            maxRows={2}
            onRemove={keys => {
              onChange?.({
                ...value,
                value: value.value.filter(item => !keys.has(item.id)),
              })
            }}
            renderEmptyState={() => (
              <Text color="neutralSecondary" size="small">
                No related {foreignList.plural.toLowerCase()}…
              </Text>
            )}
          >
            {renderItem}
          </TagGroup>
        )}
      </VStack>

      {!isReadOnly && (
        <DialogContainer onDismiss={() => setDialogOpen(false)}>
          {dialogIsOpen && (
            <BuildItemDialog
              listKey={foreignList.key}
              onChange={builtItemData => {
                const id = `_____temporary_${counter}`
                const label =
                  (builtItemData?.[foreignList.labelField] as string | null) ??
                  `[Unnamed ${foreignList.singular} ${counter}]`
                setDialogOpen(false)
                setCounter(counter + 1)

                if (value.kind === 'many') {
                  onChange({
                    ...value,
                    value: [
                      ...value.value,
                      {
                        id,
                        label,
                        data: builtItemData,
                        built: true,
                      },
                    ],
                  })
                } else if (value.kind === 'one') {
                  onChange({
                    ...value,
                    value: {
                      id,
                      label,
                      data: builtItemData,
                      built: true,
                    },
                  })
                }
              }}
            />
          )}
        </DialogContainer>
      )}
    </Fragment>
  )
}

// NOTE: fix for `TagGroup` perf issue, should typically be okay to just
// inline the render function
function renderItem(item: { id: string; href: string; label: string }) {
  if (item.href === '') return <Item>{item.label}</Item>
  return <Item href={item.href}>{item.label}</Item>
}

export const Cell: CellComponent<typeof controller> = ({ field, item }) => {
  const list = useList(field.refListKey)

  if (field.display === 'count' || field.display === 'table') {
    const count = item[`${field.path}Count`] as number
    return count != null ? <Numeral value={count} abbreviate /> : null
  }

  const data = item[field.path]
  const items = (Array.isArray(data) ? data : [data]).filter(Boolean)
  const displayItems = items.length < 3 ? items : items.slice(0, 2)
  const overflow = items.length < 3 ? 0 : items.length - 2

  return (
    <Text>
      {displayItems.map((item, index) => (
        <Fragment key={item.id}>
          {index ? ', ' : ''}
          <TextLink href={`/${list.path}/${item.id}`}>{item.label || item.id}</TextLink>
        </Fragment>
      ))}
      {overflow ? `, and ${overflow} more` : null}
    </Text>
  )
}

export function controller(
  config: FieldControllerConfig<
    {
      refFieldKey?: string
      refListKey: string
      many: boolean
      hideCreate: boolean
      refLabelField: string
      refSearchFields: string[]
    } & (
      | { displayMode: 'select' }
      | { displayMode: 'count' }
      | {
          displayMode: 'table'
          refFieldKey: string
          initialSort: { field: string; direction: 'ASC' | 'DESC' } | null
          columns: string[] | null
        }
    )
  >
): RelationshipController {
  const { listKey, path: fieldKey, label, description } = config
  const { displayMode, hideCreate, many, refFieldKey, refLabelField, refListKey, refSearchFields } =
    config.fieldMeta

  return {
    refFieldKey,
    many,
    listKey,
    path: fieldKey,
    label,
    description,
    display: displayMode,
    refLabelField,
    refSearchFields,
    refListKey,
    graphqlSelection:
      displayMode === 'count' || displayMode === 'table'
        ? `${fieldKey}Count`
        : `${fieldKey} {
              id
              label: ${refLabelField}
            }`,
    hideCreate: hideCreate || displayMode === 'table',
    columns: displayMode === 'table' ? config.fieldMeta.columns : null,
    initialSort: displayMode === 'table' ? config.fieldMeta.initialSort : null,
    // note we're not making the state kind: 'count' when ui.displayMode is set to 'count'.
    // that ui.displayMode: 'count' is really just a way to have reasonable performance
    // because our other UIs don't handle relationships with a large number of items well
    // but that's not a problem here since we're creating a new item so we might as well them a better UI
    defaultValue: many
      ? {
          kind: 'many',
          id: null,
          initialValue: [],
          value: [],
        }
      : {
          kind: 'one',
          id: null,
          value: null,
          initialValue: null,
        },
    validate(value, opts) {
      if ('count' in value) return true
      return opts.isRequired
        ? value.kind === 'one'
          ? value.value !== null
          : value.value.length > 0
        : true
    },
    deserialize: data => {
      if (displayMode === 'count' || displayMode === 'table') {
        return {
          id: data.id,
          kind: 'count',
          count: data[`${config.path}Count`] ?? 0,
        }
      }
      if (many) {
        const value = (data[config.path] || []).map((x: any) => ({
          id: x.id,
          label: x.label || x.id,
        }))
        return {
          kind: 'many',
          id: data.id,
          initialValue: value,
          value,
        }
      }
      let value = data[config.path]
      if (value) {
        value = {
          id: value.id,
          label: value.label || value.id,
        }
      }
      return {
        kind: 'one',
        id: data.id,
        value,
        initialValue: value,
      }
    },
    serialize: state => {
      if (state.kind === 'many') {
        const newAllIds = new Set(state.value.map(x => x.id))
        const initialIds = new Set(state.initialValue.map(x => x.id))
        const disconnect = state.initialValue
          .filter(x => !newAllIds.has(x.id))
          .map(x => ({ id: x.id }))
        const connect = state.value
          .filter(x => !x.built && !initialIds.has(x.id))
          .map(x => ({ id: x.id }))
        const create = state.value.filter(x => x.built).map(x => x.data)
        const output = {
          ...(disconnect.length ? { disconnect } : {}),
          ...(connect.length ? { connect } : {}),
          ...(create.length ? { create } : {}),
        }

        if (Object.keys(output).length) {
          return {
            [config.path]: output,
          }
        }
      } else if (state.kind === 'one') {
        if (state.initialValue && !state.value) return { [config.path]: { disconnect: true } }
        if (state.value?.built) {
          return {
            [config.path]: {
              create: state.value.data,
            },
          }
        }
        if (state.value && state.value.id !== state.initialValue?.id) {
          return {
            [config.path]: {
              connect: {
                id: state.value.id,
              },
            },
          }
        }
      }
      return {}
    },
    filter: {
      Filter(props) {
        const foreignList = useList(refListKey)
        if (props.type === 'empty' || props.type === 'not_empty') return null
        // TODO: show labels rather than ids
        if (props.type === 'is' || props.type === 'not_is') {
          return (
            <ComboboxSingle
              autoFocus
              aria-label={label}
              isReadOnly={false}
              labelField={refLabelField}
              searchFields={refSearchFields}
              list={foreignList}
              state={{
                kind: 'one',
                value:
                  typeof props.value === 'string'
                    ? { id: props.value, label: props.value, built: false }
                    : null,
                onChange(newItem) {
                  props.onChange(newItem === null ? null : newItem.id.toString())
                },
              }}
            />
          )
        }
        const ids = Array.isArray(props.value) ? props.value : []
        const value = ids.map((id): RelationshipValue => ({ id, label: id, built: false }))
        return (
          <VStack gap="medium">
            <ComboboxMany
              autoFocus
              aria-label={label}
              isReadOnly={false}
              labelField={refLabelField}
              searchFields={refSearchFields}
              list={foreignList}
              state={{
                kind: 'many',
                value,
                onChange(newItem) {
                  props.onChange(newItem.map(x => x.id.toString()))
                },
              }}
            />
            <TagGroup
              aria-label={`related ${foreignList.plural}`}
              items={value.map(item => ({
                id: item.id.toString() ?? '',
                label: item.label ?? '',
                href: item.built ? '' : `/${foreignList.path}/${item.id}`,
              }))}
              maxRows={2}
              onRemove={keys => {
                props.onChange(ids.filter(id => !keys.has(id)))
              }}
              renderEmptyState={() => (
                <Text color="neutralSecondary" size="small">
                  Select related {foreignList.plural.toLowerCase()}…
                </Text>
              )}
            >
              {renderItem}
            </TagGroup>
          </VStack>
        )
      },
      Label({ label, type, value }) {
        const listFormatter = useListFormatter({
          style: 'short',
          type: 'disjunction',
        })

        if (['empty', 'not_empty'].includes(type)) return label.toLowerCase()
        if (['is', 'not_is'].includes(type)) return `${label.toLowerCase()} ${value}`
        return `${label.toLowerCase()} (${listFormatter.format(value || [''])})`
      },
      graphql: ({ type, value }) => {
        if (type === 'empty' && !many) return { [config.path]: { equals: null } }
        if (type === 'empty' && many) return { [config.path]: { none: {} } }
        if (type === 'not_empty' && !many) return { [config.path]: { not: { equals: null } } }
        if (type === 'not_empty' && many) return { [config.path]: { some: {} } }
        if (type === 'is') return { [config.path]: { id: { equals: value } } }
        if (type === 'not_is') return { [config.path]: { not: { id: { equals: value } } } }
        if (type === 'some') return { [config.path]: { some: { id: { in: value } } } }
        if (type === 'not_some') return { [config.path]: { not: { some: { id: { in: value } } } } }
        return { [config.path]: { [type]: value } } // uh
      },
      parseGraphQL: () => [],
      types: {
        empty: { label: 'Is empty', initialValue: null },
        not_empty: { label: 'Is not empty', initialValue: null },
        ...(many
          ? {
              some: { label: 'Is one of', initialValue: [] },
              not_some: { label: 'Is not one of', initialValue: [] },
            }
          : {
              is: { label: 'Is', initialValue: null },
              not_is: { label: 'Is not', initialValue: null },
            }),
      },
    },
  }
}
</file>

<file path="packages/core/src/fields/types/relationship/views/RelationshipTable.tsx">
import { useMemo, useState } from 'react'
import type { SortDescriptor } from '@keystar/ui/table'
import { Cell, Column, Row, TableBody, TableHeader, TableView } from '@keystar/ui/table'
import { Field as KeystarField } from '@keystar/ui/field'
import { useList } from '../../../../admin-ui/context'
import type { controller } from '.'
import { textSelectIcon } from '@keystar/ui/icon/icons/textSelectIcon'
import { EmptyState } from '../../../../admin-ui/components/EmptyState'
import { gql, useQuery } from '../../../../admin-ui/apollo'
import { Text } from '@keystar/ui/typography'
import { GraphQLErrorNotice } from '../../../../admin-ui/components'
import { PaginationControls } from '../../../../___internal-do-not-use-will-break-in-patch/admin-ui/pages/ListPage/PaginationControls'
import type { CountRelationshipValue } from './types'
import { useRelatedItemHref, useRelatedItemLabel } from './ContextualActions'
import { ActionButton } from '@keystar/ui/button'
import { Icon } from '@keystar/ui/icon'
import { arrowUpRightIcon } from '@keystar/ui/icon/icons/arrowUpRightIcon'
import { TooltipTrigger, Tooltip } from '@keystar/ui/tooltip'
import { ProgressCircle } from '@keystar/ui/progress'

export function RelationshipTable({
  field,
  value,
}: {
  field: ReturnType<typeof controller>
  value: CountRelationshipValue
}) {
  if (!field.refFieldKey) {
    throw new Error('refFieldKey is required for displayMode: table')
  }
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(50)
  const list = useList(field.refListKey)
  const hasManyOnRefList: boolean = (list.fields[field.refFieldKey].controller as any).many

  const selectedFields = field.columns ?? list.initialColumns
  const columns = useMemo(() => {
    return selectedFields.map(path => {
      const field = list.fields[path]
      return {
        id: path,
        label: field.label,
        allowsSorting: field.isOrderable,
      }
    })
  }, [selectedFields, list])

  const [sort, setSort] = useState<SortDescriptor>(() => {
    if (field.initialSort) {
      return {
        column: field.initialSort.field,
        direction: field.initialSort.direction === 'ASC' ? 'ascending' : 'descending',
      }
    }
    return { column: 'id', direction: 'ascending' }
  })

  const { data, error, loading } = useQuery(
    useMemo(() => {
      const selectedGqlFields = [...selectedFields]
        .map(fieldPath => list.fields[fieldPath].controller.graphqlSelection)
        .join('\n')

      // TODO: FIXME: this is bad
      return gql`
          query (
            $where: ${list.graphql.names.whereInputName},
            $take: Int!,
            $skip: Int!,
            $orderBy: [${list.graphql.names.listOrderName}!]
          ) {
            items: ${list.graphql.names.listQueryName}(
              where: $where,
              take: $take,
              skip: $skip,
              orderBy: $orderBy
            ) {
              ${selectedFields.includes('id') ? '' : 'id'}
              ${selectedGqlFields}
            }
          }
        `
    }, [list, selectedFields]),
    {
      fetchPolicy: 'cache-and-network',
      errorPolicy: 'all',
      variables: {
        where: {
          [field.refFieldKey]: hasManyOnRefList
            ? { some: { id: { equals: value.id } } }
            : { id: { equals: value.id } },
        },
        take: pageSize,
        skip: (currentPage - 1) * pageSize,
        orderBy: [{ [sort.column]: sort.direction === 'ascending' ? 'asc' : 'desc' }],
      },
    }
  )
  const relatedItemLabel = useRelatedItemLabel(field)
  const relatedItemHref = useRelatedItemHref({ field, value })
  const items: Record<string, unknown>[] = data?.items ?? []
  return (
    <KeystarField label={field.label} description={field.description}>
      {inputProps => (
        <>
          <TableView
            {...inputProps}
            selectionMode="none"
            onSortChange={sort => setSort(sort)}
            sortDescriptor={sort}
            density="compact"
            UNSAFE_style={{ minHeight: 29 * 10, maxHeight: 29 * 10 }}
            overflowMode="truncate"
            renderEmptyState={() =>
              loading ? (
                <ProgressCircle isIndeterminate />
              ) : error ? (
                <GraphQLErrorNotice errors={[error.networkError, ...(error.graphQLErrors ?? [])]} />
              ) : (
                <EmptyState
                  icon={textSelectIcon}
                  title="Empty related items"
                  message="There are no related items."
                />
              )
            }
            flex
          >
            <TableHeader columns={columns}>
              {({ label, id, ...options }) => (
                <Column key={id} isRowHeader {...options}>
                  {label}
                </Column>
              )}
            </TableHeader>
            <TableBody items={items}>
              {row => {
                return (
                  <Row href={`/${list.path}/${row?.id}`}>
                    {key => {
                      const field = list.fields[key]
                      const value = row[key]
                      const CellContent = field.views.Cell
                      return (
                        <Cell>
                          {CellContent ? (
                            <CellContent value={value} field={field.controller} item={row} />
                          ) : (
                            <Text>{value?.toString()}</Text>
                          )}
                        </Cell>
                      )
                    }}
                  </Row>
                )
              }}
            </TableBody>
          </TableView>
          <PaginationControls
            currentPage={currentPage}
            pageSize={pageSize}
            plural={list.plural}
            singular={list.singular}
            total={value.count}
            onChangePage={page => setCurrentPage(page)}
            onChangePageSize={size => setPageSize(size)}
            extraActions={
              <TooltipTrigger>
                <ActionButton href={relatedItemHref!}>
                  <Icon src={arrowUpRightIcon} />
                </ActionButton>
                <Tooltip>{relatedItemLabel}</Tooltip>
              </TooltipTrigger>
            }
          />
        </>
      )}
    </KeystarField>
  )
}
</file>

<file path="packages/core/src/fields/types/relationship/views/types.ts">
import type { FieldController } from '../../../../types'

export type RelationshipValue = {
  id: string
  label: string | null
  data?: Record<string, unknown>
  built: undefined | boolean
}

export type SingleRelationshipValue = {
  kind: 'one'
  id: null | string
  initialValue: RelationshipValue | null
  value: RelationshipValue | null
}

export type ManyRelationshipValue = {
  kind: 'many'
  id: null | string
  initialValue: RelationshipValue[]
  value: RelationshipValue[]
}

export type CountRelationshipValue = {
  kind: 'count'
  id: string
  count: number
}

export type RelationshipController = FieldController<
  ManyRelationshipValue | SingleRelationshipValue | CountRelationshipValue,
  string[] | (string | null) // | number // TODO: count
> & {
  display: 'select' | 'count' | 'table'
  listKey: string
  refListKey: string
  refFieldKey?: string
  refLabelField: string
  refSearchFields: string[]
  hideCreate: boolean
  many: boolean
  columns: string[] | null
  initialSort: { field: string; direction: 'ASC' | 'DESC' } | null
}
</file>

<file path="packages/core/src/fields/types/relationship/views/useApolloQuery.ts">
import { useEffect, useMemo, useState } from 'react'

import type { ListMeta } from '../../../../types'
import {
  type TypedDocumentNode,
  ApolloClient,
  gql,
  InMemoryCache,
  useApolloClient,
  useQuery,
} from '../../../../admin-ui/apollo'
import { useSearchFilter } from './useFilter'
import { type RelationshipValue } from './types'

function useDebouncedValue<T>(value: T, limitMs: number) {
  const [debouncedValue, setDebouncedValue] = useState(() => value)

  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedValue(() => value)
    }, limitMs)
    return () => clearTimeout(timeout)
  }, [value, limitMs])

  return debouncedValue
}

export function useApolloQuery(args: {
  labelField: string
  list: ListMeta
  searchFields: string[]
  state:
    | { kind: 'many'; value: RelationshipValue[] }
    | { kind: 'one'; value: RelationshipValue | null }
}) {
  const { labelField, list, searchFields, state } = args
  const [search, setSearch] = useState(() => {
    if (state.kind === 'one' && state.value?.label) return state.value?.label
    return ''
  })

  const QUERY: TypedDocumentNode<
    { items: { id: string; label: string | null }[]; count: number },
    { where: Record<string, any>; take: number; skip: number }
  > = gql`
    query RelationshipSelect($where: ${list.graphql.names.whereInputName}!, $take: Int!, $skip: Int!) {
      items: ${list.graphql.names.listQueryName}(where: $where, take: $take, skip: $skip) {
        id: id
        label: ${labelField}
      }
      count: ${list.graphql.names.listQueryCountName}(where: $where)
    }
  `

  const debouncedSearch = useDebouncedValue(search, 200)
  const manipulatedSearch =
    state.kind === 'one' && state.value?.label === debouncedSearch ? '' : debouncedSearch
  const where = useSearchFilter(manipulatedSearch, list, searchFields)

  const link = useApolloClient().link
  // we're using a local apollo client here because writing a global implementation of the typePolicies
  // would require making assumptions about how pagination should work which won't always be right
  const apolloClient = useMemo(
    () =>
      new ApolloClient({
        link,
        cache: new InMemoryCache({
          typePolicies: {
            Query: {
              fields: {
                [list.graphql.names.listQueryName]: {
                  keyArgs: ['where'],
                  merge: (existing: readonly unknown[], incoming: readonly unknown[], { args }) => {
                    const merged = existing ? existing.slice() : []
                    const { skip } = args!
                    for (let i = 0; i < incoming.length; ++i) {
                      merged[skip + i] = incoming[i]
                    }
                    return merged
                  },
                },
              },
            },
          },
        }),
      }),
    [link, list.graphql.names.listQueryName]
  )

  const initialItemsToLoad = Math.min(list.pageSize, 10)
  const subsequentItemsToLoad = Math.min(list.pageSize, 50)
  const { data, previousData, error, loading, fetchMore } = useQuery(QUERY, {
    fetchPolicy: 'network-only',
    variables: { where, take: initialItemsToLoad, skip: 0 },
    client: apolloClient,
  })

  // we want to avoid fetching more again and `loading` from Apollo
  // doesn't seem to become true when fetching more
  const [lastFetchMore, setLastFetchMore] = useState<{
    where: Record<string, any>
    list: ListMeta
    skip: number
  } | null>(null)

  const count = data?.count || 0
  const onLoadMore = () => {
    const skip = data?.items.length
    if (
      !loading &&
      skip &&
      data.items.length < count &&
      (lastFetchMore?.where !== where ||
        lastFetchMore?.list !== list ||
        lastFetchMore?.skip !== skip)
    ) {
      const QUERY: TypedDocumentNode<
        { items: { id: string; label: string | null }[] },
        { where: Record<string, any>; take: number; skip: number }
      > = gql`
        query RelationshipSelectMore($where: ${list.graphql.names.whereInputName}!, $take: Int!, $skip: Int!) {
          items: ${list.graphql.names.listQueryName}(where: $where, take: $take, skip: $skip) {
            label: ${labelField}
            id: id
          }
        }
      `
      setLastFetchMore({ list, skip, where })
      fetchMore({
        query: QUERY,
        variables: {
          where,
          take: subsequentItemsToLoad,
          skip,
        },
      })
        .then(() => setLastFetchMore(null))
        .catch(() => setLastFetchMore(null))
    }
  }

  return {
    data: loading ? previousData : data,
    error,
    loading,
    loadingState: getLoadingState({ loading, search }),
    search,
    setSearch,
    onLoadMore,
  }
}

function getLoadingState(options: { loading: boolean; search: string }): LoadingState {
  if (options.loading) {
    if (options.search.length) return 'filtering'
    return 'loading'
  }

  return 'idle'
}

type LoadingState = 'loading' | 'sorting' | 'loadingMore' | 'error' | 'idle' | 'filtering'
</file>

<file path="packages/core/src/fields/types/relationship/views/useFilter.tsx">
import { useMemo } from 'react'
import { useKeystone } from '../../../../admin-ui/context'

import type { ListMeta } from '../../../../types'

function isInt(x: string) {
  return Number.isInteger(Number(x))
}

function isBigInt(x: string) {
  try {
    BigInt(x)
    return true
  } catch {
    return true
  }
}

// TODO: this is unfortunate, remove in breaking change?
function isUuid(x: unknown) {
  if (typeof x !== 'string') return
  if (x.length !== 36) return
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(x)
}

export function useSearchFilter(value: string, list: ListMeta, searchFields: string[]) {
  const { adminMeta } = useKeystone()
  const { lists = {} } = adminMeta ?? {}
  return useMemo(() => {
    const trimmedSearch = value.trim()
    if (!trimmedSearch.length) return { OR: [] }

    const conditions: Record<string, any>[] = []
    const idField = list.fields.id.fieldMeta as { type: string; kind: string }

    if (idField.type === 'String') {
      // TODO: remove in breaking change?
      if (idField.kind === 'uuid') {
        if (isUuid(value)) {
          conditions.push({ id: { equals: trimmedSearch } })
        }
      } else {
        conditions.push({ id: { equals: trimmedSearch } })
      }
    } else if (idField.type === 'Int' && isInt(trimmedSearch)) {
      conditions.push({ id: { equals: Number(trimmedSearch) } })
    } else if (idField.type === 'BigInt' && isBigInt(trimmedSearch)) {
      conditions.push({ id: { equals: trimmedSearch } })
    }

    for (const fieldKey of searchFields) {
      const field = list.fields[fieldKey]

      // @ts-expect-error TODO: fix fieldMeta type for relationship fields
      if (field.fieldMeta?.refSearchFields) {
        const {
          // @ts-expect-error TODO: fix fieldMeta type for relationship fields
          refListKey,
          // @ts-expect-error TODO: fix fieldMeta type for relationship fields
          refSearchFields,
          // @ts-expect-error TODO: fix fieldMeta type for relationship fields
          many = false,
        } = field.fieldMeta
        const refList = lists[refListKey]

        for (const refFieldKey of refSearchFields) {
          const refField = refList.fields[refFieldKey]
          if (!refField.search) continue // WARNING: we dont support depth > 2
          // @ts-expect-error TODO: fix fieldMeta type for relationship fields
          if (refField.fieldMeta?.refSearchFields) continue // WARNING: we dont support depth > 2

          if (many) {
            conditions.push({
              [fieldKey]: {
                some: {
                  [refFieldKey]: {
                    contains: trimmedSearch,
                    mode: refField.search === 'insensitive' ? 'insensitive' : undefined,
                  },
                },
              },
            })

            continue
          }

          conditions.push({
            [fieldKey]: {
              [refFieldKey]: {
                contains: trimmedSearch,
                mode: refField.search === 'insensitive' ? 'insensitive' : undefined,
              },
            },
          })
        }

        continue
      }

      conditions.push({
        [field.path]: {
          contains: trimmedSearch,
          mode: field.search === 'insensitive' ? 'insensitive' : undefined,
        },
      })
    }

    return { OR: conditions }
  }, [value, list, searchFields])
}
</file>

<file path="packages/core/src/fields/types/relationship/index.ts">
import { g } from '../../..'
import {
  type ListMetaSource,
  getAdminMetaForRelationshipField,
} from '../../../lib/create-admin-meta'
import type { JSONValue } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
} from '../../../types'
import type { controller } from './views'

// This is the default display mode for Relationships
type SelectDisplayConfig = {
  ui?: {
    // Sets the relationship to display as a Select field
    displayMode?: 'select'
    /**
     * The path of the field to use from the related list for item labels in the select.
     * Defaults to the labelField configured on the related list.
     */
    labelField?: string
    searchFields?: string[]
  }
}

type CountDisplayConfig = {
  many: true
  ui?: {
    // Sets the relationship to display as a count
    displayMode: 'count'
    itemView: {
      fieldMode: 'read'
    }
  }
}

type TableDisplayConfig = {
  ref: `${string}.${string}`
  many: true
  ui?: {
    displayMode: 'table'
    itemView: {
      fieldMode: 'read'
    }
    initialSort?: { field: string; direction: 'ASC' | 'DESC' }
    columns?: string[]
  }
}

type OneDbConfig = {
  many?: false
  db?: {
    extendPrismaSchema?: (field: string) => string
    foreignKey?:
      | true
      | {
          map: string
        }
  }
}

type ManyDbConfig = {
  many: true
  db?: {
    relationName?: string
    extendPrismaSchema?: (field: string) => string
  }
}

function throwIfMissingFields(
  localListMeta: ListMetaSource,
  foreignListMeta: ListMetaSource,
  refLabelField: string,
  refSearchFields: string[],
  fieldKey: string
) {
  if (!(refLabelField in foreignListMeta.fieldsByKey)) {
    throw new Error(
      `"${refLabelField}" is not a field of list "${foreignListMeta.key}", configured as labelField for "${localListMeta.key}.${fieldKey}"`
    )
  }

  for (const searchFieldKey of refSearchFields) {
    const field = foreignListMeta.fieldsByKey[searchFieldKey]
    if (!field)
      throw new Error(
        `"${searchFieldKey}" is not a field of list "${foreignListMeta.key}", configured as searchField for "${localListMeta.key}.${fieldKey}"`
      )

    if (field.search === null)
      throw new Error(
        `"${searchFieldKey}" is not a searchable field of list "${foreignListMeta.key}", configured as searchField for "${localListMeta.key}.${fieldKey}"`
      )
  }
}

type ArrayOr<T> = T | T[]

// TODO: add types based on list types
type FieldTypeInfo = {
  item: undefined
  inputs: {
    where: any
    create: JSONValue | undefined
    update: JSONValue | undefined
    uniqueWhere: undefined
    orderBy: undefined
  }
  prisma: {
    create:
      | {
          connect?: ArrayOr<{ id?: string; [key: string]: unknown }>
          create?: any
          set?: ArrayOr<{ id?: string; [key: string]: unknown }>
        }
      | undefined
      | null
    update:
      | {
          connect?: ArrayOr<{ id?: string; [key: string]: unknown }>
          create?: any
          set?: ArrayOr<{ id?: string; [key: string]: unknown }>
          disconnect?: boolean | ArrayOr<{ id?: string; [key: string]: unknown }> | undefined
        }
      | undefined
      | null
  }
}

export type RelationshipFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> & {
  many?: boolean
  ref: string
  ui?: {
    hideCreate?: boolean
  }
} & (OneDbConfig | ManyDbConfig) &
  (SelectDisplayConfig | CountDisplayConfig | TableDisplayConfig)

export function relationship<ListTypeInfo extends BaseListTypeInfo>({
  ref,
  ...config
}: RelationshipFieldConfig<ListTypeInfo>): FieldTypeFunc<ListTypeInfo> {
  const { many = false } = config
  const [foreignListKey, foreignFieldKey] = ref.split('.') as [string, string | undefined]

  return ({ fieldKey, listKey, lists }) => {
    const foreignList = lists[foreignListKey]
    if (!foreignList)
      throw new Error(`${listKey}.${fieldKey} points to ${ref}, but ${ref} doesn't exist`)

    const foreignListTypes = foreignList.types
    const commonConfig = {
      ...config,
      __ksTelemetryFieldTypeName: '@keystone-6/relationship',
      views: '@keystone-6/core/fields/types/relationship/views',
      getAdminMeta: (): Parameters<typeof controller>[0]['fieldMeta'] => {
        const adminMetaRoot = getAdminMetaForRelationshipField()
        const localListMeta = adminMetaRoot.listsByKey[listKey]
        const foreignListMeta = adminMetaRoot.listsByKey[foreignListKey]

        if (!foreignListMeta) {
          throw new Error(`The ref [${ref}] on relationship [${listKey}.${fieldKey}] is invalid`)
        }

        const refLabelField = foreignListMeta.labelField
        const refSearchFields = foreignListMeta.initialSearchFields

        const hasOmittedCreate =
          !lists[foreignListKey].types.relateTo[many ? 'many' : 'one'].create.getFields().create
        const hideCreate = config.ui?.hideCreate ?? hasOmittedCreate
        if (!hideCreate && hasOmittedCreate) {
          throw new Error(
            `${listKey}.${fieldKey} has ui.hideCreate: false, but the related list ${foreignListKey} has graphql.omit.create: true`
          )
        }

        if (config.ui?.displayMode === 'count') {
          if (config.ui.itemView?.fieldMode !== 'read') {
            throw new Error(
              `displayMode: 'count' on relationship fields requires itemView.fieldMode to be 'read' but ${listKey}.${fieldKey} does not have this set`
            )
          }
          return {
            displayMode: 'count',
            refListKey: foreignListKey,
            refFieldKey: foreignFieldKey,
            many,
            hideCreate,
            refLabelField,
            refSearchFields,
          }
        }

        if (config.ui?.displayMode === 'table') {
          if (!foreignFieldKey) {
            throw new Error(
              `Using a two-sided relationship (\`ref\` must specify "List.fieldKey", not just "List") is required when using displayMode: 'table' for relationship fields but ${listKey}.${fieldKey} has \`ref: ${JSON.stringify(ref)}\``
            )
          }
          if (config.ui.itemView?.fieldMode !== 'read') {
            throw new Error(
              `displayMode: 'table' on relationship fields currently requires itemView.fieldMode to be 'read' but ${listKey}.${fieldKey} does not have this set`
            )
          }
          for (const key of config.ui.columns ?? []) {
            if (!(key in foreignListMeta.fieldsByKey)) {
              throw new Error(
                `The field "${foreignListMeta.key}.${key}" does not exist, configured as a column for "${localListMeta.key}.${fieldKey}"`
              )
            }
          }
          if (config.ui.initialSort) {
            const field = foreignListMeta.fieldsByKey[config.ui.initialSort.field]
            if (!field) {
              throw new Error(
                `The field "${foreignListMeta.key}.${config.ui.initialSort.field}" does not exist, configured as the initialSort field for "${localListMeta.key}.${fieldKey}"`
              )
            }
            if (!field.isOrderable) {
              throw new Error(
                `The field "${foreignListMeta.key}.${config.ui.initialSort.field}" is not orderable, configured as the initialSort field for "${localListMeta.key}.${fieldKey}"`
              )
            }
          }
          return {
            displayMode: 'table',
            refListKey: foreignListKey,
            refFieldKey: foreignFieldKey,
            initialSort: config.ui.initialSort ?? foreignListMeta.initialSort ?? null,
            columns: config.ui.columns ?? null,
            many,
            hideCreate,
            refLabelField,
            refSearchFields,
          }
        }

        // prefer the local definition to the foreign list, if provided
        const specificRefLabelField = config.ui?.labelField || refLabelField
        const specificRefSearchFields = config.ui?.searchFields || refSearchFields
        throwIfMissingFields(
          localListMeta,
          foreignListMeta,
          specificRefLabelField,
          specificRefSearchFields,
          fieldKey
        )
        return {
          displayMode: 'select',
          refListKey: foreignListKey,
          refFieldKey: foreignFieldKey,
          many,
          hideCreate,
          refLabelField: specificRefLabelField,
          refSearchFields: specificRefSearchFields,
        }
      },
    }

    if (config.many) {
      return fieldType({
        kind: 'relation',
        mode: 'many',
        list: foreignListKey,
        field: foreignFieldKey,
        relationName: config.db?.relationName,
        extendPrismaSchema: config.db?.extendPrismaSchema,
      })({
        ...commonConfig,
        input: {
          where: {
            arg: g.arg({ type: foreignListTypes.relateTo.many.where }),
            resolve(value, _context, resolve) {
              return resolve(value)
            },
          },
          create: {
            arg: g.arg({ type: foreignListTypes.relateTo.many.create }),
            async resolve(value, _context, resolve) {
              return resolve(value)
            },
          },
          update: {
            arg: g.arg({ type: foreignListTypes.relateTo.many.update }),
            async resolve(value, _context, resolve) {
              return resolve(value)
            },
          },
        },
        output: g.field({
          args: foreignListTypes.findManyArgs,
          type: g.list(g.nonNull(foreignListTypes.output)),
          resolve({ value }, args) {
            return value.findMany(args)
          },
        }),
        extraOutputFields: {
          [`${fieldKey}Count`]: g.field({
            type: g.Int,
            args: {
              where: g.arg({
                type: g.nonNull(foreignListTypes.where),
                defaultValue: {},
              }),
            },
            resolve({ value }, args) {
              return value.count({
                where: args.where,
              })
            },
          }),
        },
      })
    }

    return fieldType({
      kind: 'relation',
      mode: 'one',
      list: foreignListKey,
      field: foreignFieldKey,
      foreignKey: config.db?.foreignKey,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...commonConfig,
      input: {
        where: {
          arg: g.arg({ type: foreignListTypes.where }),
          resolve(value, _context, resolve) {
            return resolve(value)
          },
        },
        uniqueWhere: {
          arg: g.arg({ type: foreignListTypes.uniqueWhere }),
        },

        create: foreignListTypes.relateTo.one.create && {
          arg: g.arg({ type: foreignListTypes.relateTo.one.create }),
          async resolve(value, _context, resolve) {
            return resolve(value)
          },
        },

        update: foreignListTypes.relateTo.one.update && {
          arg: g.arg({ type: foreignListTypes.relateTo.one.update }),
          async resolve(value, _context, resolve) {
            return resolve(value)
          },
        },
      },
      output: g.field({
        type: foreignListTypes.output,
        resolve({ value }) {
          return value()
        },
      }),
    })
  }
}
</file>

<file path="packages/core/src/fields/types/select/views/index.tsx">
import { type Key, useMemo, useState } from 'react'
import { useListFormatter } from '@react-aria/i18n'

import { ListView } from '@keystar/ui/list-view'
import { FieldLabel } from '@keystar/ui/field'
import { VStack } from '@keystar/ui/layout'
import { Item, Picker } from '@keystar/ui/picker'
import { Radio, RadioGroup } from '@keystar/ui/radio'
import { tokenSchema } from '@keystar/ui/style'
import { Text } from '@keystar/ui/typography'

import { NullableFieldWrapper } from '../../../../admin-ui/components'
import { SegmentedControl } from './SegmentedControl'

import type {
  CellComponent,
  FieldController,
  FieldControllerConfig,
  FieldProps,
  SimpleFieldTypeInfo,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, forceValidation, onChange, value, isRequired } = props
  const [isDirty, setDirty] = useState(false)
  const [preNullValue, setPreNullValue] = useState(
    value.value || (value.kind === 'update' ? value.initial : null)
  )
  const longestLabelLength = useMemo(() => {
    return field.options.reduce((a, item) => Math.max(a, item.label.length), 0)
  }, [field.options])

  const selectedKey = value.value?.value || preNullValue?.value || null
  const isNullable = !isRequired
  const isNull = isNullable && value.value?.value == null
  const isInvalid = !validate(value, isRequired)
  const isReadOnly = onChange == null
  const errorMessage =
    isInvalid && (isDirty || forceValidation) ? `${field.label} is required.` : undefined

  const onSelectionChange = (key: Key) => {
    if (!onChange) return

    // FIXME: the value should be primitive, not an object. i think this is an
    // artefact from react-select’s API
    const newValue: Value['value'] = field.options.find(opt => opt.value === key) ?? null

    // allow clearing the value if the field is not required
    // if (!field.isRequired && key === selectedKey) {
    //   newValue = null
    // }

    onChange({ ...value, value: newValue })
    setDirty(true)
  }
  // TODO: this would benefit from a similar treatment to the text field's
  // `{ kind: 'null', prev: string }` solution
  const onNullChange = (isChecked: boolean) => {
    if (!onChange) return

    if (isChecked) {
      onChange({ ...value, value: null })
      setPreNullValue(value.value)
    } else {
      onChange({ ...value, value: preNullValue || field.options[0] })
    }
    setDirty(true)
  }

  const fieldElement = (() => {
    switch (field.displayMode) {
      case 'segmented-control':
        return (
          <SegmentedControl
            label={field.label}
            description={field.description}
            errorMessage={errorMessage}
            isDisabled={isNull}
            isReadOnly={isReadOnly}
            isRequired={isRequired}
            items={field.options}
            onChange={onSelectionChange}
            value={selectedKey}
            textValue={field.options.find(item => item.value === selectedKey)?.label || ''}
          >
            {item => <Item key={item.value}>{item.label}</Item>}
          </SegmentedControl>
        )
      case 'radio':
        return (
          <RadioGroup
            label={field.label}
            description={field.description}
            errorMessage={errorMessage}
            isDisabled={isNull}
            isReadOnly={isReadOnly}
            isRequired={isRequired}
            onChange={onSelectionChange}
            // maintain the previous value when set to null in aid of continuity
            // for the user. it will be cleared when the item is saved
            value={value.value?.value ?? preNullValue?.value}
          >
            {field.options.map(item => (
              <Radio key={item.value} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </RadioGroup>
        )
      default:
        return (
          <Picker
            autoFocus={autoFocus}
            label={field.label}
            description={field.description}
            errorMessage={errorMessage}
            isDisabled={isNull}
            isReadOnly={isReadOnly}
            isRequired={isRequired}
            items={field.options}
            onSelectionChange={onSelectionChange}
            selectedKey={selectedKey}
            flex={{ mobile: true, desktop: 'initial' }}
            UNSAFE_style={{
              fontSize: tokenSchema.typography.text.regular.size,
              width: `clamp(${tokenSchema.size.alias.singleLineWidth}, calc(${longestLabelLength}ex + ${tokenSchema.size.icon.regular}), 100%)`,
            }}
          >
            {item => <Item key={item.value}>{item.label}</Item>}
          </Picker>
        )
    }
  })()

  return (
    <NullableFieldWrapper
      isAllowed={!isRequired}
      autoFocus={isNull && autoFocus}
      label={field.label}
      isReadOnly={isReadOnly}
      isNull={isNull}
      onChange={onNullChange}
    >
      {fieldElement}
    </NullableFieldWrapper>
  )
}

export const Cell: CellComponent<typeof controller> = ({ value, field }) => {
  const label = field.options.find(x => x.value === value)?.label
  return <Text>{label}</Text>
}

export type AdminSelectFieldMeta = {
  options: readonly { label: string; value: string | number }[]
  type: 'string' | 'integer' | 'enum'
  displayMode: 'select' | 'segmented-control' | 'radio'
  defaultValue: string | number | null
}

type Config = FieldControllerConfig<AdminSelectFieldMeta>
type Option = { label: string; value: string }
type Value =
  | { value: Option | null; kind: 'create' }
  | { value: Option | null; initial: Option | null; kind: 'update' }

function validate(value: Value, isRequired: boolean) {
  if (isRequired) {
    // if you got null initially on the update screen, we want to allow saving
    // since the user probably doesn't have read access control
    if (value.kind === 'update' && value.initial === null) return true
    return value.value !== null
  }
  return true
}

const FILTER_TYPES = {
  matches: {
    label: 'Matches',
    initialValue: [],
  },
  not_matches: {
    label: 'Does not match',
    initialValue: [],
  },
}

export function controller(config: Config): FieldController<
  Value,
  string[],
  SimpleFieldTypeInfo<'String'>['inputs']['where']
> & {
  options: Option[]
  type: 'string' | 'integer' | 'enum'
  displayMode: 'select' | 'segmented-control' | 'radio'
} {
  const optionsWithStringValues = config.fieldMeta.options.map(x => ({
    label: x.label,
    value: x.value.toString(),
  }))

  // Transform from string value to type appropriate value
  const t = (v: string | null) =>
    v === null ? null : config.fieldMeta.type === 'integer' ? parseInt(v) : v

  const stringifiedDefault = config.fieldMeta.defaultValue?.toString()

  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    defaultValue: {
      kind: 'create',
      value: optionsWithStringValues.find(x => x.value === stringifiedDefault) ?? null,
    },
    type: config.fieldMeta.type,
    displayMode: config.fieldMeta.displayMode,
    options: optionsWithStringValues,
    deserialize: data => {
      for (const option of config.fieldMeta.options) {
        if (option.value === data[config.path]) {
          const stringifiedOption = { label: option.label, value: option.value.toString() }
          return {
            kind: 'update',
            initial: stringifiedOption,
            value: stringifiedOption,
          }
        }
      }
      return { kind: 'update', initial: null, value: null }
    },
    serialize: value => ({ [config.path]: t(value.value?.value ?? null) }),
    validate: (value, opts) => validate(value, opts.isRequired),
    filter: {
      Filter(props) {
        const { autoFocus, context, typeLabel, onChange, value, type, ...otherProps } = props

        const densityLevels = ['spacious', 'regular', 'compact'] as const
        const density =
          densityLevels[Math.min(Math.floor((optionsWithStringValues.length - 1) / 3), 2)]
        const listView = (
          <ListView
            aria-label={typeLabel}
            density={density}
            items={optionsWithStringValues}
            flex
            minHeight={0}
            maxHeight="100%"
            selectionMode="multiple"
            onSelectionChange={selection => {
              if (selection === 'all') return // irrelevant for this case

              onChange([...selection].filter(x => typeof x === 'string'))
            }}
            selectedKeys={value}
            {...otherProps}
          >
            {item => <Item key={item.value}>{item.label}</Item>}
          </ListView>
        )

        if (context === 'edit') {
          return (
            <VStack gap="medium" flex minHeight={0} maxHeight="100%">
              {/* intentionally not linked: the `ListView` has an explicit "aria-label" to avoid awkwardness with IDs and forked render */}
              <FieldLabel elementType="span">{typeLabel}</FieldLabel>
              {listView}
            </VStack>
          )
        }

        return listView
      },
      graphql: ({ type, value: options }) => ({
        [config.path]: {
          [type === 'not_matches' ? 'notIn' : 'in']: options.map(x => t(x)),
        },
      }),
      parseGraphQL(value) {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (type === 'equals' && value != null) {
            return { type: 'matches', value: [value] }
          }
          if (type === 'notIn' || type === 'in') {
            if (!value) return []
            return {
              type: type === 'notIn' ? 'not_matches' : 'matches',
              value: value.filter(x => x != null),
            }
          }
          return []
        })
      },
      Label({ type, value }) {
        const listFormatter = useListFormatter({
          style: 'short',
          type: 'disjunction',
        })

        if (value.length === 0) {
          return type === 'not_matches' ? `is set` : `is not set`
        }
        const values = new Set(value)
        const labels = optionsWithStringValues
          .filter(opt => values.has(opt.value))
          .map(i => i.label)
        const prefix = type === 'not_matches' ? `is not` : `is`

        if (value.length === 1) return `${prefix} ${labels[0]}`
        if (value.length === 2) return `${prefix} ${listFormatter.format(labels)}`
        return `${prefix} ${listFormatter.format([labels[0], `${value.length - 1} more`])}`
      },
      types: FILTER_TYPES,
    },
  }
}
</file>

<file path="packages/core/src/fields/types/select/views/SegmentedControl.tsx">
import { useField } from '@react-aria/label'

import { type ActionGroupProps, ActionGroup } from '@keystar/ui/action-group'
import { type FieldProps, FieldPrimitive } from '@keystar/ui/field'
import { TextField } from '@keystar/ui/text-field'

type Key = number | string // React.Key now includes bigint, which isn't supported by @react-aria

type SegmentedControlProps<T> = FieldProps &
  Pick<ActionGroupProps<T>, 'children' | 'items'> & {
    value: Key | null
    onChange: (value: Key) => void
    /** The `textValue` is used to display the selected item label in read-only mode. */
    textValue?: string
  }

export function SegmentedControl<T>(props: SegmentedControlProps<T>) {
  const {
    children,
    isDisabled,
    isReadOnly,
    isRequired,
    label,
    description,
    errorMessage,
    value,
    items,
    onChange,
    textValue,
    ...otherProps
  } = props
  const { labelProps, fieldProps, descriptionProps, errorMessageProps } = useField(props)
  const selectedKeys = value ? [value] : []

  // The `ActionGroup` isn’t really designed for use within forms, so we need to
  // handle read-only mode. There's probably a better solution but this will at
  // least be accessible.
  if (isReadOnly) {
    return (
      <TextField
        description={description}
        errorMessage={errorMessage}
        isReadOnly={isReadOnly}
        isRequired={isRequired}
        label={label}
        value={textValue}
      />
    )
  }

  return (
    <FieldPrimitive
      description={description}
      descriptionProps={descriptionProps}
      errorMessage={errorMessage}
      errorMessageProps={errorMessageProps}
      isRequired={isRequired}
      label={label}
      labelElementType="span"
      labelProps={labelProps}
      {...otherProps}
    >
      <ActionGroup
        {...fieldProps}
        density="compact"
        disallowEmptySelection
        isDisabled={isDisabled}
        overflowMode="collapse"
        selectionMode="single"
        items={items}
        onSelectionChange={selection => {
          if (selection === 'all') return // irrelevant for single-select
          const next = selection.values().next().value
          if (!next) return
          onChange(next)
        }}
        selectedKeys={selectedKeys}
      >
        {children}
      </ActionGroup>
    </FieldPrimitive>
  )
}
</file>

<file path="packages/core/src/fields/types/select/index.ts">
import { classify } from 'inflection'
import { humanize } from '../../../lib/utils'
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { filters } from '../../filters'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import type { controller } from './views'

export type SelectFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'String' | 'Int'>
> &
  (
    | {
        /**
         * When a value is provided as just a string, it will be formatted in the same way
         * as field labels are to create the label.
         */
        options: readonly ({ label: string; value: string } | string)[]
        /**
         * If `enum` is provided on SQLite, it will use an enum in GraphQL but a string in the database.
         */
        type?: 'string' | 'enum'
        defaultValue?: string
      }
    | {
        options: readonly { label: string; value: number }[]
        type: 'integer'
        defaultValue?: number
      }
  ) & {
    ui?: {
      displayMode?: 'select' | 'segmented-control' | 'radio'
    }

    validation?: {
      /**
       * @default false
       */
      isRequired?: boolean
    }
    isIndexed?: boolean | 'unique'
    db?: {
      isNullable?: boolean
      map?: string
      extendPrismaSchema?: (field: string) => string
    }
  }

// these are the lowest and highest values for a signed 32-bit integer
const MAX_INT = 2147483647
const MIN_INT = -2147483648

export function select<ListTypeInfo extends BaseListTypeInfo>(
  config: SelectFieldConfig<ListTypeInfo>
): FieldTypeFunc<ListTypeInfo> {
  const { isIndexed, ui: { displayMode = 'select', ...ui } = {}, defaultValue, validation } = config

  return meta => {
    const options = config.options.map(option => {
      if (typeof option === 'string') {
        return {
          label: humanize(option),
          value: option,
        }
      }
      return option
    })

    const accepted = new Set(options.map(x => x.value))
    if (accepted.size !== options.length) {
      throw new Error(`${meta.listKey}.${meta.fieldKey}: duplicate options, this is not allowed`)
    }

    const { mode, validate } = makeValidateHook(
      meta,
      config,
      ({ resolvedData, operation, addValidationError }) => {
        if (operation === 'delete') return

        const value = resolvedData[meta.fieldKey]
        if (value != null && !accepted.has(value)) {
          addValidationError(`value is not an accepted option`)
        }
      }
    )

    const commonConfig = {
      ...config,
      mode,
      ...defaultIsRequired({ ui }, validation?.isRequired ?? false),
      hooks: {
        ...config.hooks,
        validate,
      },
      __ksTelemetryFieldTypeName: '@keystone-6/select',
      views: '@keystone-6/core/fields/types/select/views',
      getAdminMeta: (): Parameters<typeof controller>[0]['fieldMeta'] => ({
        options,
        type: config.type ?? 'string',
        displayMode: displayMode,
        defaultValue: defaultValue ?? null,
      }),
    }

    const commonDbFieldConfig = {
      mode,
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      default:
        defaultValue === undefined
          ? undefined
          : { kind: 'literal' as const, value: defaultValue as any },
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    } as const

    const resolveCreate = <T extends string | number>(val: T | null | undefined): T | null => {
      if (val === undefined) {
        return (defaultValue as T | undefined) ?? null
      }
      return val
    }

    if (config.type === 'integer') {
      if (
        config.options.some(
          ({ value }) => !Number.isInteger(value) || value > MAX_INT || value < MIN_INT
        )
      ) {
        throw new Error(
          `${meta.listKey}.${meta.fieldKey} specifies integer values that are outside the range of a 32-bit signed integer`
        )
      }
      return fieldType({
        kind: 'scalar',
        scalar: 'Int',
        ...commonDbFieldConfig,
      })({
        ...commonConfig,
        input: {
          uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.Int }) } : undefined,
          where: {
            arg: g.arg({ type: filters[meta.provider].Int[mode] }),
            resolve: mode === 'required' ? undefined : filters.resolveCommon,
          },
          create: {
            arg: g.arg({
              type: g.Int,
              defaultValue: typeof defaultValue === 'number' ? defaultValue : undefined,
            }),
            resolve: resolveCreate,
          },
          update: { arg: g.arg({ type: g.Int }) },
          orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
        },
        output: g.field({ type: g.Int }),
      })
    }

    if (config.type === 'enum') {
      const enumName = `${meta.listKey}${classify(meta.fieldKey)}Type`
      const enumValues = options.map(x => `${x.value}`)

      const graphQLType = g.enum({
        name: enumName,
        values: g.enumValues(enumValues),
      })
      return fieldType(
        meta.provider === 'sqlite'
          ? { kind: 'scalar', scalar: 'String', ...commonDbFieldConfig }
          : {
              kind: 'enum',
              values: enumValues,
              name: enumName,
              ...commonDbFieldConfig,
            }
      )({
        ...commonConfig,
        input: {
          uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: graphQLType }) } : undefined,
          where: {
            arg: g.arg({ type: filters[meta.provider].enum(graphQLType).optional }),
            resolve: mode === 'required' ? undefined : filters.resolveCommon,
          },
          create: {
            arg: g.arg({
              type: graphQLType,
              defaultValue: typeof defaultValue === 'string' ? defaultValue : undefined,
            }),
            resolve: resolveCreate,
          },
          update: { arg: g.arg({ type: graphQLType }) },
          orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
        },
        output: g.field({ type: graphQLType }),
      })
    }

    return fieldType({ kind: 'scalar', scalar: 'String', ...commonDbFieldConfig })({
      ...commonConfig,
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.String }) } : undefined,
        where: {
          arg: g.arg({ type: filters[meta.provider].String[mode] }),
          resolve: mode === 'required' ? undefined : filters.resolveString,
        },
        create: {
          arg: g.arg({
            type: g.String,
            defaultValue: typeof defaultValue === 'string' ? defaultValue : undefined,
          }),
          resolve: resolveCreate,
        },
        update: { arg: g.arg({ type: g.String }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({ type: g.String }),
    })
  }
}
</file>

<file path="packages/core/src/fields/types/text/views/index.tsx">
import { useState } from 'react'
import { TextArea, TextField } from '@keystar/ui/text-field'

import type {
  FieldController,
  FieldControllerConfig,
  FieldProps,
  SimpleFieldTypeInfo,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'
import { NullableFieldWrapper } from '../../../../admin-ui/components'
import type { TextFieldMeta } from '..'

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, forceValidation, onChange, value, isRequired } = props

  const [shouldShowErrors, setShouldShowErrors] = useState(false)
  const validationMessages = validate(value, field.validation, props.isRequired, field.label)

  const isReadOnly = onChange == null
  const isNull = value.inner.kind === 'null'
  const isTextArea = field.displayMode === 'textarea'
  const FieldComponent = isTextArea ? TextArea : TextField

  return (
    <NullableFieldWrapper
      isAllowed={field.isNullable}
      autoFocus={isNull && autoFocus}
      label={field.label}
      isReadOnly={isReadOnly}
      isNull={isNull}
      onChange={() => {
        if (!onChange) return

        const inner =
          value.inner.kind === 'value'
            ? ({ kind: 'null', prev: value.inner.value } as const)
            : ({ kind: 'value', value: value.inner.prev } as const)

        onChange({ ...value, inner })
      }}
    >
      <FieldComponent
        autoFocus={autoFocus}
        description={field.description}
        label={field.label}
        errorMessage={
          !!validationMessages.length && (shouldShowErrors || forceValidation)
            ? validationMessages.join('. ')
            : undefined
        }
        isDisabled={isNull}
        isReadOnly={isReadOnly}
        isRequired={isRequired}
        onBlur={() => {
          setShouldShowErrors(true)
        }}
        onChange={textValue => {
          if (!onChange) return
          onChange({
            ...value,
            inner: {
              kind: 'value',
              value: textValue,
            },
          })
        }}
        // maintain the previous value when set to null in aid of continuity for
        // the user. it will be cleared when the item is saved
        value={value.inner.kind === 'value' ? value.inner.value : value.inner.prev}
      />
    </NullableFieldWrapper>
  )
}

type Config = FieldControllerConfig<TextFieldMeta>

type Validation = {
  match: { regex: RegExp; explanation: string | null } | null
  length: { min: number | null; max: number | null }
}

function validate(
  value: TextValue,
  validation: Validation,
  isRequired: boolean,
  fieldLabel: string
): string[] {
  // if the value is the same as the initial for an update, we don't want to block saving
  // since we're not gonna send it anyway if it's the same
  // and going "fix this thing that is unrelated to the thing you're doing" is bad
  // and also bc it could be null bc of read access control
  if (
    value.kind === 'update' &&
    ((value.initial.kind === 'null' && value.inner.kind === 'null') ||
      (value.initial.kind === 'value' &&
        value.inner.kind === 'value' &&
        value.inner.value === value.initial.value))
  ) {
    return []
  }

  if (value.inner.kind === 'null') {
    if (isRequired) return [`${fieldLabel} is required`]
    return []
  }

  const val = value.inner.value
  const min = Math.max(validation.length.min ?? 0, isRequired ? 1 : 0)
  const messages: string[] = []
  if (val.length < min) {
    if (min === 1) {
      messages.push(`${fieldLabel} must not be empty`)
    } else {
      messages.push(`${fieldLabel} must be at least ${min} characters long`)
    }
  }
  if (validation.length.max !== null && val.length > validation.length.max) {
    messages.push(`${fieldLabel} must be no longer than ${validation.length.max} characters`)
  }
  if (validation.match && !validation.match.regex.test(val)) {
    messages.push(
      validation.match.explanation || `${fieldLabel} must match ${validation.match.regex}`
    )
  }
  return messages
}

type InnerTextValue = { kind: 'null'; prev: string } | { kind: 'value'; value: string }
type TextValue =
  | { kind: 'create'; inner: InnerTextValue }
  | { kind: 'update'; inner: InnerTextValue; initial: InnerTextValue }

function deserializeTextValue(value: string | null): InnerTextValue {
  if (value === null) return { kind: 'null', prev: '' }
  return { kind: 'value', value }
}

export function controller(config: Config): FieldController<
  TextValue,
  string,
  SimpleFieldTypeInfo<'String'>['inputs']['where']
> & {
  displayMode: 'input' | 'textarea'
  validation: Validation
  isNullable: boolean
} {
  const validation: Validation = {
    length: config.fieldMeta.validation.length,
    match: config.fieldMeta.validation.match
      ? {
          regex: new RegExp(
            config.fieldMeta.validation.match.regex.source,
            config.fieldMeta.validation.match.regex.flags
          ),
          explanation: config.fieldMeta.validation.match.explanation,
        }
      : null,
  }
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    defaultValue: { kind: 'create', inner: deserializeTextValue(config.fieldMeta.defaultValue) },
    displayMode: config.fieldMeta.displayMode,
    isNullable: config.fieldMeta.isNullable,
    deserialize: data => {
      const inner = deserializeTextValue(data[config.path])
      return { kind: 'update', inner, initial: inner }
    },
    serialize: value => ({ [config.path]: value.inner.kind === 'null' ? null : value.inner.value }),
    validation,
    validate: (val, opts) => validate(val, validation, opts.isRequired, config.label).length === 0,
    filter: {
      Filter(props) {
        const { autoFocus, context, typeLabel, onChange, type, value, ...otherProps } = props

        const labelProps =
          context === 'add' ? { label: config.label, description: typeLabel } : { label: typeLabel }

        // NOTE: "type" is a valid attribute for an input element, however the
        // prop represents a filter type in this context e.g. "contains_i", so
        // we're filtering it out of the spread.
        // TODO: more consideration is needed for the filter API, once
        // requirements are better understood.
        return (
          <TextField
            {...otherProps}
            {...labelProps}
            autoFocus={autoFocus}
            onChange={onChange}
            value={value}
          />
        )
      },
      Label({ label, value }) {
        const trimmedLabel = label.toLowerCase().replace(' exactly', '')
        return `${trimmedLabel} "${value}"`
      },

      graphql: ({ type, value }) => {
        const isNot = type.startsWith('not_')
        const key =
          type === 'is_i' || type === 'not_i'
            ? 'equals'
            : type
                .replace(/_i$/, '')
                .replace('not_', '')
                .replace(/_([a-z])/g, (_, char: string) => char.toUpperCase())
        const filter = { [key]: value }
        return {
          [config.path]: {
            ...(isNot ? { not: filter } : filter),
            mode: config.fieldMeta.shouldUseModeInsensitive ? 'insensitive' : undefined,
          },
        }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (!value) return []
          if (type === 'equals') return { type: 'is_i', value }
          if (type === 'contains') return { type: 'contains_i', value }
          if (type === 'startsWith') return { type: 'starts_with_i', value }
          if (type === 'endsWith') return { type: 'ends_with_i', value }
          if (type === 'not') {
            if (value?.equals) return { type: 'not_i', value: value.equals }
            if (value?.contains) return { type: 'not_contains_i', value: value.contains }
            if (value?.startsWith) return { type: 'not_starts_with_i', value: value.startsWith }
            if (value?.endsWith) return { type: 'not_ends_with_i', value: value.endsWith }
          }
          return []
        })
      },
      types: {
        contains_i: {
          label: 'Contains',
          initialValue: '',
        },
        not_contains_i: {
          label: 'Does not contain',
          initialValue: '',
        },
        is_i: {
          label: 'Is exactly',
          initialValue: '',
        },
        not_i: {
          label: 'Is not exactly',
          initialValue: '',
        },
        starts_with_i: {
          label: 'Starts with',
          initialValue: '',
        },
        not_starts_with_i: {
          label: 'Does not start with',
          initialValue: '',
        },
        ends_with_i: {
          label: 'Ends with',
          initialValue: '',
        },
        not_ends_with_i: {
          label: 'Does not end with',
          initialValue: '',
        },
      },
    },
  }
}
</file>

<file path="packages/core/src/fields/types/text/index.ts">
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import { filters } from '../../filters'

export type TextFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'String'>
> & {
  isIndexed?: boolean | 'unique'
  ui?: {
    displayMode?: 'input' | 'textarea'
  }
  validation?: {
    /**
     * Makes the field disallow null values and require a string at least 1 character long
     */
    isRequired?: boolean
    match?: { regex: RegExp; explanation?: string }
    length?: { min?: number; max?: number }
  }
  defaultValue?: string | null
  db?: {
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
    /**
     * The underlying database type.
     * Only some of the types are supported on PostgreSQL and MySQL.
     * The native type is not customisable on SQLite.
     * See Prisma's documentation for more information about the supported types.
     *
     * https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string
     */
    nativeType?:
      | 'Text' // PostgreSQL and MySQL
      | `VarChar(${number})`
      | `Char(${number})`
      | `Bit(${number})` // PostgreSQL
      | 'VarBit'
      | 'Uuid'
      | 'Xml'
      | 'Inet'
      | 'Citext'
      | 'TinyText' // MySQL
      | 'MediumText'
      | 'LargeText'
  }
}

export type TextFieldMeta = {
  displayMode: 'input' | 'textarea'
  shouldUseModeInsensitive: boolean
  isNullable: boolean
  validation: {
    match: { regex: { source: string; flags: string }; explanation: string | null } | null
    length: { min: number | null; max: number | null }
  }
  defaultValue: string | null
}

export function text<ListTypeInfo extends BaseListTypeInfo>(
  config: TextFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { defaultValue: defaultValue_, isIndexed, validation = {} } = config

  config.db ??= {}
  config.db.isNullable ??= false // TODO: sigh, remove in breaking change?

  const isRequired = validation.isRequired ?? false
  const match = validation.match
  const min = validation.isRequired ? (validation.length?.min ?? 1) : validation.length?.min
  const max = validation.length?.max

  return meta => {
    if (min !== undefined && (!Number.isInteger(min) || min < 0)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.length.min: ${min} but it must be a positive integer`
      )
    }
    if (max !== undefined && (!Number.isInteger(max) || max < 0)) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.length.max: ${max} but it must be a positive integer`
      )
    }
    if (isRequired && min !== undefined && min === 0) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.isRequired: true and validation.length.min: 0, this is not allowed because validation.isRequired implies at least a min length of 1`
      )
    }
    if (isRequired && max !== undefined && max === 0) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies validation.isRequired: true and validation.length.max: 0, this is not allowed because validation.isRequired implies at least a max length of 1`
      )
    }
    if (min !== undefined && max !== undefined && min > max) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} specifies a validation.length.max that is less than the validation.length.min, and therefore has no valid options`
      )
    }

    // defaulted to false as a zero length string is preferred to null
    const isNullable = config.db?.isNullable ?? false
    const defaultValue = isNullable ? (defaultValue_ ?? null) : (defaultValue_ ?? '')
    const hasAdditionalValidation = match || min !== undefined || max !== undefined
    const { mode, validate } = makeValidateHook(
      meta,
      config,
      hasAdditionalValidation
        ? ({ resolvedData, operation, addValidationError }) => {
            if (operation === 'delete') return

            const value = resolvedData[meta.fieldKey]
            if (value != null) {
              if (min !== undefined && value.length < min) {
                if (min === 1) {
                  addValidationError(`value must not be empty`)
                } else {
                  addValidationError(`value must be at least ${min} characters long`)
                }
              }
              if (max !== undefined && value.length > max) {
                addValidationError(`value must be no longer than ${max} characters`)
              }
              if (match && !match.regex.test(value)) {
                addValidationError(match.explanation ?? `value must match ${match.regex}`)
              }
            }
          }
        : undefined
    )

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: 'String',
      default: defaultValue === null ? undefined : { kind: 'literal', value: defaultValue },
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      map: config.db?.map,
      nativeType: config.db?.nativeType,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, isRequired),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.String }) } : undefined,
        where: {
          arg: g.arg({
            type: filters[meta.provider].String[mode],
          }),
          resolve: mode === 'required' ? undefined : filters.resolveString,
        },
        create: {
          arg: g.arg({
            type: g.String,
            defaultValue: typeof defaultValue === 'string' ? defaultValue : undefined,
          }),
          resolve(val) {
            if (val !== undefined) return val
            return defaultValue ?? null
          },
        },
        update: { arg: g.arg({ type: g.String }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({
        type: g.String,
      }),
      __ksTelemetryFieldTypeName: '@keystone-6/text',
      views: '@keystone-6/core/fields/types/text/views',
      getAdminMeta(): TextFieldMeta {
        return {
          displayMode: config.ui?.displayMode ?? 'input',
          shouldUseModeInsensitive: meta.provider === 'postgresql',
          validation: {
            match: match
              ? {
                  regex: {
                    source: match.regex.source,
                    flags: match.regex.flags,
                  },
                  explanation: match.explanation ?? `value must match ${match.regex}`,
                }
              : null,
            length: {
              max: max ?? null,
              min: min ?? null,
            },
          },
          defaultValue: defaultValue ?? (isNullable ? null : ''),
          isNullable,
        }
      },
    })
  }
}
</file>

<file path="packages/core/src/fields/types/timestamp/views/__tests__/index.tsx">
import { controller } from '../index'

const STUBCONFIG = {
  listKey: 'timestamp',
  path: './timestamp',
  label: 'foo',
  customViews: {},
  description: null,
}

describe('controller', () => {
  describe('validate', () => {
    it('null is OK if not required', () => {
      const { validate } = controller({
        ...STUBCONFIG,
        fieldMeta: {
          defaultValue: null,
          updatedAt: false,
        },
      })
      expect(
        validate!(
          {
            kind: 'create',
            value: null,
          },
          { isRequired: false }
        )
      ).toBe(true)
    })
    it('isRequired enforces required (null)', () => {
      const { validate } = controller({
        ...STUBCONFIG,
        fieldMeta: {
          defaultValue: null,
          updatedAt: false,
        },
      })
      expect(
        validate!(
          {
            kind: 'create',
            value: null,
          },
          { isRequired: true }
        )
      ).toBe('foo is required')
    })
    it('isRequired enforces required (value)', () => {
      const { validate } = controller({
        ...STUBCONFIG,
        fieldMeta: {
          defaultValue: null,
          updatedAt: false,
        },
      })
      expect(
        validate!(
          {
            kind: 'create',
            value: new Date().toJSON(),
          },
          { isRequired: true }
        )
      ).toBe(true)
    })
  })
})
</file>

<file path="packages/core/src/fields/types/timestamp/views/__tests__/utils.tsx">
import { parseISO } from 'date-fns'
import { constructTimestamp } from '../utils'

const STUBVALIDDATE = '2020-10-31'
const STUBVALIDTIME = '10:00'

describe('constructTimestamp()', () => {
  it('should throw on empty values', () => {
    expect(() => constructTimestamp({ dateValue: '', timeValue: '' })).toThrow()
    expect(() => constructTimestamp({ dateValue: '', timeValue: '09:30' })).toThrow()
  })
  it('should take two valid timestamp values and construct them into a valid ISO string', () => {
    const result = constructTimestamp({ dateValue: STUBVALIDDATE, timeValue: STUBVALIDTIME })
    expect(Boolean(parseISO(result).toISOString())).toBe(true)
  })
})
</file>

<file path="packages/core/src/fields/types/timestamp/views/index.tsx">
import { getLocalTimeZone, now, parseAbsoluteToLocal } from '@internationalized/date'
import { useDateFormatter } from '@react-aria/i18n'
import { useReducer, useState } from 'react'

import { ToggleButton } from '@keystar/ui/button'
import { DatePicker } from '@keystar/ui/date-time'
import { Icon } from '@keystar/ui/icon'
import { calendarClockIcon } from '@keystar/ui/icon/icons/calendarClockIcon'
import { Grid } from '@keystar/ui/layout'
import { TextField } from '@keystar/ui/text-field'
import { TooltipTrigger, Tooltip } from '@keystar/ui/tooltip'
import { Text } from '@keystar/ui/typography'

import type {
  CellComponent,
  FieldController,
  FieldControllerConfig,
  FieldProps,
  SimpleFieldTypeInfo,
} from '../../../../types'
import { entriesTyped } from '../../../../lib/core/utils'
import type { Value } from './utils'

export function Field(props: FieldProps<typeof controller>) {
  const { field, value, forceValidation, onChange, isRequired } = props
  const parsedValue = value.value ? parseAbsoluteToLocal(value.value) : null

  const [isDirty, setDirty] = useState(false)
  const [isReadonlyUTC, toggleReadonlyUTC] = useReducer(prev => !prev, false)
  const dateFormatter = useDateFormatter({ dateStyle: 'long', timeStyle: 'long' })

  // the read-only date field is deceptively interactive, better to render a
  // text field to avoid confusion. when there's no value the field is disabled,
  // placeholder text is shown, and the toggle button is hidden
  if (!onChange) {
    return (
      <Grid
        columns={parsedValue ? 'minmax(0, 1fr) auto' : undefined}
        gap="regular"
        alignItems="end"
      >
        <TextField
          label={field.label}
          description={field.description}
          isDisabled={!parsedValue}
          isReadOnly
          value={
            parsedValue
              ? isReadonlyUTC
                ? parsedValue.toAbsoluteString()
                : dateFormatter.format(parsedValue.toDate())
              : 'yyyy-mm-dd --:--:--'
          }
        />
        {!!parsedValue && (
          <TooltipTrigger>
            <ToggleButton
              aria-label="utc time"
              isSelected={isReadonlyUTC}
              onPress={toggleReadonlyUTC}
            >
              <Icon src={calendarClockIcon} />
            </ToggleButton>
            <Tooltip>Local / UTC</Tooltip>
          </TooltipTrigger>
        )}
      </Grid>
    )
  }

  const showValidation = isDirty || forceValidation
  const validationMessage = showValidation
    ? validate(value, field.fieldMeta, isRequired, field.label)
    : undefined

  return (
    <DatePicker
      label={field.label}
      description={field.description}
      errorMessage={showValidation ? validationMessage : undefined}
      granularity="second"
      // isReadOnly={undefined} // read-only state handled above
      isRequired={isRequired}
      // NOTE: in addition to providing a cue for users about the expected input
      // format, the `placeholderValue` determines the type of value for the
      // field. the implementation below ensures `ZonedDateTime` so we can avoid
      // unnecessary guards or transformations.
      placeholderValue={now(getLocalTimeZone())}
      onBlur={() => setDirty(true)}
      onChange={datetime => {
        onChange({ ...value, value: datetime?.toAbsoluteString() ?? null })
      }}
      value={parsedValue}
    />
  )
}

function validate(
  value: Value,
  fieldMeta: TimestampFieldMeta,
  isRequired: boolean,
  label: string
): string | undefined {
  const isEmpty = !value.value

  // if we recieve null initially on the item view and the current value is null,
  // we should always allow saving it because:
  // - the value might be null in the database and we don't want to prevent saving the whole item because of that
  // - we might have null because of an access control error
  if (value.kind === 'update' && value.initial === null && isEmpty) return

  if (
    value.kind === 'create' &&
    isEmpty &&
    ((typeof fieldMeta.defaultValue === 'object' && fieldMeta.defaultValue?.kind === 'now') ||
      fieldMeta.updatedAt)
  )
    return

  if (isRequired && isEmpty) return `${label} is required`

  // TODO: update field in "@keystar/ui" to use new validation APIs, for more
  // granular validation messages
  return
}

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  const dateFormatter = useDateFormatter({ dateStyle: 'medium', timeStyle: 'short' })
  return value ? <Text>{dateFormatter.format(new Date(value))}</Text> : null
}

export type TimestampFieldMeta = {
  defaultValue: string | { kind: 'now' } | null
  updatedAt: boolean
}

export function controller(config: FieldControllerConfig<TimestampFieldMeta>): FieldController<
  Value,
  string | null,
  SimpleFieldTypeInfo<'DateTime'>['inputs']['where']
> & {
  fieldMeta: TimestampFieldMeta
} {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: config.path,
    fieldMeta: config.fieldMeta,
    defaultValue: {
      kind: 'create',
      value:
        typeof config.fieldMeta.defaultValue === 'string' ? config.fieldMeta.defaultValue : null,
    },
    deserialize: data => {
      const value = data[config.path]
      return {
        kind: 'update',
        initial: data[config.path],
        value: value ?? null,
      }
    },
    serialize: ({ value }) => {
      if (value) return { [config.path]: value }
      return { [config.path]: null }
    },
    validate: (value, opts) =>
      validate(value, config.fieldMeta, opts.isRequired, config.label) === undefined,
    filter: {
      Filter(props) {
        const {
          autoFocus,
          context,
          forceValidation,
          typeLabel,
          onChange,
          value,
          type,
          ...otherProps
        } = props
        const [isDirty, setDirty] = useState(false)

        if (type === 'empty' || type === 'not_empty') return null
        const parsedValue = value ? parseAbsoluteToLocal(value) : null

        return (
          <DatePicker
            label={typeLabel}
            granularity="second"
            placeholderValue={now(getLocalTimeZone())}
            errorMessage={(forceValidation || isDirty) && !value ? 'Required' : null}
            isRequired
            hideTimeZone
            onBlur={() => setDirty(true)}
            onChange={datetime => {
              onChange(datetime?.toAbsoluteString() ?? null)
            }}
            value={parsedValue}
            {...otherProps}
          />
        )
      },
      graphql: ({ type, value }) => {
        if (type === 'empty') return { [config.path]: { equals: null } }
        if (type === 'not_empty') return { [config.path]: { not: { equals: null } } }
        if (type === 'not') return { [config.path]: { not: { equals: value } } }
        return { [config.path]: { [type]: value } }
      },
      parseGraphQL: value => {
        return entriesTyped(value).flatMap(([type, value]) => {
          if (type === 'equals' && value === null) {
            return { type: 'empty', value: null }
          }
          if (!value) return []
          if (type === 'equals') return { type: 'equals', value: value as unknown as string }
          if (type === 'not') {
            if (value?.equals === null) return { type: 'not_empty', value: null }
            return { type: 'not', value: value.equals as unknown as string }
          }
          if (type === 'gt' || type === 'lt') {
            return { type, value: value as unknown as string }
          }
          return []
        })
      },
      Label({ label, type, value }) {
        const dateFormatter = useDateFormatter({ dateStyle: 'short', timeStyle: 'short' })
        if (type === 'empty' || type === 'not_empty' || value == null) {
          return label.toLocaleLowerCase()
        }

        return `${label.toLocaleLowerCase()} ${dateFormatter.format(new Date(value))}`
      },
      types: {
        equals: {
          label: 'Is exactly',
          initialValue: null,
        },
        not: {
          label: 'Is not exactly',
          initialValue: null,
        },
        lt: {
          label: 'Is before',
          initialValue: null,
        },
        gt: {
          label: 'Is after',
          initialValue: null,
        },
        empty: {
          label: 'Is empty',
          initialValue: null,
        },
        not_empty: {
          label: 'Is not empty',
          initialValue: null,
        },
      },
    },
  }
}
</file>

<file path="packages/core/src/fields/types/timestamp/views/utils.ts">
export function constructTimestamp({
  dateValue,
  timeValue,
}: {
  dateValue: string
  timeValue: string
}) {
  return new Date(`${dateValue}T${timeValue}`).toISOString()
}

export type InnerValue = string | null

export type Value =
  | {
      kind: 'create'
      value: string | null
    }
  | {
      kind: 'update'
      value: string | null
      initial: string | null
    }
</file>

<file path="packages/core/src/fields/types/timestamp/index.ts">
import type { SimpleFieldTypeInfo } from '../../../types'
import {
  type BaseListTypeInfo,
  type FieldTypeFunc,
  type CommonFieldConfig,
  fieldType,
  orderDirectionEnum,
} from '../../../types'
import { g } from '../../..'
import { filters } from '../../filters'
import { makeValidateHook, defaultIsRequired } from '../../non-null-graphql'
import { type TimestampFieldMeta } from './views'

export type TimestampFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  SimpleFieldTypeInfo<'DateTime' | 'String'> // TODO: make more exact
> & {
  isIndexed?: boolean | 'unique'
  validation?: {
    isRequired?: boolean
  }
  defaultValue?: string | { kind: 'now' }
  db?: {
    // this is @updatedAt in Prisma
    updatedAt?: boolean
    isNullable?: boolean
    map?: string
    extendPrismaSchema?: (field: string) => string
  }
}

export function timestamp<ListTypeInfo extends BaseListTypeInfo>(
  config: TimestampFieldConfig<ListTypeInfo> = {}
): FieldTypeFunc<ListTypeInfo> {
  const { isIndexed, defaultValue, validation } = config

  return meta => {
    if (typeof defaultValue === 'string') {
      try {
        g.DateTime.parseValue(defaultValue)
      } catch (err) {
        throw new Error(
          `${meta.listKey}.${meta.fieldKey}.defaultValue is required to be an ISO8601 date-time string such as ${new Date().toISOString()}`
        )
      }
    }

    const parsedDefaultValue =
      typeof defaultValue === 'string'
        ? (g.DateTime.parseValue(defaultValue) as Date)
        : defaultValue
    const { mode, validate } = makeValidateHook(meta, config)

    return fieldType({
      kind: 'scalar',
      mode,
      scalar: 'DateTime',
      index: isIndexed === true ? 'index' : isIndexed || undefined,
      default:
        typeof defaultValue === 'string'
          ? {
              kind: 'literal',
              value: defaultValue,
            }
          : defaultValue === undefined
            ? undefined
            : { kind: 'now' },
      updatedAt: config.db?.updatedAt,
      map: config.db?.map,
      extendPrismaSchema: config.db?.extendPrismaSchema,
    })({
      ...config,
      ...defaultIsRequired(config, validation?.isRequired ?? false),
      hooks: {
        ...config.hooks,
        validate,
      },
      input: {
        uniqueWhere: isIndexed === 'unique' ? { arg: g.arg({ type: g.DateTime }) } : undefined,
        where: {
          arg: g.arg({ type: filters[meta.provider].DateTime[mode] }),
          resolve: mode === 'optional' ? filters.resolveCommon : undefined,
        },
        create: {
          arg: g.arg({
            type: g.DateTime,
            // TODO: add support for defaultValue of { kind: 'now' } in the GraphQL API
            defaultValue: parsedDefaultValue instanceof Date ? parsedDefaultValue : undefined,
          }),
          resolve(val) {
            if (val === undefined) {
              if (parsedDefaultValue === undefined && config.db?.updatedAt) return undefined
              if (parsedDefaultValue instanceof Date || parsedDefaultValue === undefined) {
                return parsedDefaultValue ?? null
              }

              return new Date()
            }
            return val
          },
        },
        update: { arg: g.arg({ type: g.DateTime }) },
        orderBy: { arg: g.arg({ type: orderDirectionEnum }) },
      },
      output: g.field({ type: g.DateTime }),
      __ksTelemetryFieldTypeName: '@keystone-6/timestamp',
      views: '@keystone-6/core/fields/types/timestamp/views',
      getAdminMeta(): TimestampFieldMeta {
        return {
          defaultValue: defaultValue ?? null,
          updatedAt: config.db?.updatedAt ?? false,
        }
      },
    })
  }
}
</file>

<file path="packages/core/src/fields/types/virtual/views/index.tsx">
import { TextField } from '@keystar/ui/text-field'
import { Text } from '@keystar/ui/typography'
import type {
  CellComponent,
  FieldController,
  FieldControllerConfig,
  FieldProps,
} from '../../../../types'

function stringify(value: unknown) {
  if (typeof value === 'string') return value
  if (value === undefined || value === null) return ''
  if (typeof value !== 'object') return JSON.stringify(value)

  const omitTypename = (key: string, value: any) => (key === '__typename' ? undefined : value)
  const dataWithoutTypename = JSON.parse(JSON.stringify(value), omitTypename)
  return JSON.stringify(dataWithoutTypename, null, 2)
}

export function Field(props: FieldProps<typeof controller>) {
  const { autoFocus, field, value } = props
  if (value === createViewValue) return null

  return (
    <TextField
      autoFocus={autoFocus}
      description={field.description}
      label={field.label}
      isReadOnly={true}
      value={stringify(value)}
    />
  )
}

export const Cell: CellComponent<typeof controller> = ({ value }) => {
  return value != null ? <Text>{stringify(value)}</Text> : null
}

const createViewValue = Symbol('create view virtual field value')

export function controller(
  config: FieldControllerConfig<{ query: string }>
): FieldController<unknown> {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: `${config.path}${config.fieldMeta.query}`,
    defaultValue: createViewValue,
    deserialize: data => data[config.path],
    serialize: () => ({}),
  }
}
</file>

<file path="packages/core/src/fields/types/virtual/index.ts">
import { getNamedType, isLeafType } from 'graphql'
import {
  type BaseItem,
  type BaseListTypeInfo,
  type CommonFieldConfig,
  type FieldTypeFunc,
  type KeystoneContext,
  type ListGraphQLTypes,
  fieldType,
} from '../../../types'
import { g } from '../../..'
import type { GArg, GField, GInputType, GObjectType, GOutputType } from '@graphql-ts/schema'
import { GNonNull } from '@graphql-ts/schema'
import type { controller } from './views'

type VirtualFieldGraphQLField<Item extends BaseItem, Context extends KeystoneContext> = GField<
  Item,
  any,
  GOutputType<Context>,
  unknown,
  Context
>

type FieldTypeInfo = {
  item: undefined
  inputs: {
    where: undefined
    create: undefined
    update: undefined
    uniqueWhere: undefined
    orderBy: undefined
  }
  prisma: {
    create: undefined
    update: undefined
  }
}

export type VirtualFieldConfig<ListTypeInfo extends BaseListTypeInfo> = CommonFieldConfig<
  ListTypeInfo,
  FieldTypeInfo
> & {
  field:
    | VirtualFieldGraphQLField<ListTypeInfo['item'], KeystoneContext<ListTypeInfo['all']>>
    | ((lists: {
        [Key in keyof ListTypeInfo['all']['lists']]: ListGraphQLTypes<
          ListTypeInfo['all']['lists'][Key]
        >
      }) => VirtualFieldGraphQLField<ListTypeInfo['item'], KeystoneContext<ListTypeInfo['all']>>)
  unreferencedConcreteInterfaceImplementations?: readonly GObjectType<
    any,
    KeystoneContext<ListTypeInfo['all']>
  >[]
  ui?: {
    /**
     * This can be used by the AdminUI to fetch the relevant sub-fields
     *   or arguments on a non-scalar field GraphQL type
     * ```graphql
     * query {
     *   ${list}(where: { id: "..." }) {
     *     ${field}${ui.query}
     *   }
     * }
     * ```
     */
    query?: string
  }
}

export function virtual<ListTypeInfo extends BaseListTypeInfo>({
  field,
  ...config
}: VirtualFieldConfig<ListTypeInfo>): FieldTypeFunc<ListTypeInfo> {
  return meta => {
    const usableField = typeof field === 'function' ? field(meta.lists) : field
    const namedType = getNamedType(usableField.type)
    const hasRequiredArgs =
      usableField.args &&
      Object.values(usableField.args as Record<string, GArg<GInputType, boolean>>).some(
        x => x.type instanceof GNonNull && x.defaultValue === undefined
      )

    if (
      (!isLeafType(namedType) || hasRequiredArgs) &&
      !config.ui?.query &&
      (config.ui?.itemView?.fieldMode !== 'hidden' || config.ui?.listView?.fieldMode !== 'hidden')
    ) {
      throw new Error(
        `${meta.listKey}.${meta.fieldKey} requires ui.query, or ui.listView.fieldMode and ui.itemView.fieldMode to be set to 'hidden'`
      )
    }

    return fieldType({ kind: 'none' })({
      ...config,
      output: g.field({
        ...(usableField as any),
        resolve({ item }, ...args) {
          return usableField.resolve!(item, ...args)
        },
      }),
      __ksTelemetryFieldTypeName: '@keystone-6/virtual',
      views: '@keystone-6/core/fields/types/virtual/views',
      getAdminMeta: (): Parameters<typeof controller>[0]['fieldMeta'] => ({
        query: config.ui?.query ?? '',
      }),
    })
  }
}
</file>

</files>
