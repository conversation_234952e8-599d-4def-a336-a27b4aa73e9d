This file is a merged representation of the entire codebase, combined into a single document by <PERSON>omix.
The content has been processed where security check has been disabled.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
cards/
  index.tsx
  InlineCreate.tsx
  InlineEdit.tsx
  useItemState.tsx
index.tsx
RelationshipSelect.tsx
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="cards/index.tsx">
/** @jsxRuntime classic */
/** @jsx jsx */

import { type ReactNode } from 'react'
import {
  Box,
  type BoxProps,
  Stack,
  Text,
  jsx,
  useTheme,
  forwardRefWithAs,
  VisuallyHidden,
} from '@keystone-ui/core'

import { FieldContainer, FieldLabel } from '@keystone-ui/fields'
import { Button } from '@keystone-ui/button'
import { Tooltip } from '@keystone-ui/tooltip'
import { LoadingDots } from '@keystone-ui/loading'
import { useEffect, useRef, useState } from 'react'
import { type FieldProps, type ListMeta } from '../../../../../types'
import {
  getRootGraphQLFieldsFromFieldController,
  makeDataGetter,
} from '../../../../../admin-ui/utils'
import { Link } from '../../../../../admin-ui/router'
import { gql, useApolloClient } from '../../../../../admin-ui/apollo'
import { type controller } from '../index'
import { RelationshipSelect } from '../RelationshipSelect'
import { useItemState } from './useItemState'
import { InlineEdit } from './InlineEdit'
import { InlineCreate } from './InlineCreate'

type CardContainerProps = {
  children: ReactNode
  mode: 'view' | 'create' | 'edit'
} & BoxProps
const CardContainer = forwardRefWithAs(({ mode = 'view', ...props }: CardContainerProps, ref) => {
  const { tones } = useTheme()

  const tone = tones[mode === 'edit' ? 'active' : mode === 'create' ? 'positive' : 'passive']

  return (
    <Box
      ref={ref}
      paddingLeft="xlarge"
      css={{
        position: 'relative',

        ':before': {
          content: '" "',
          backgroundColor: tone.border,
          borderRadius: 4,
          width: 4,
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 1,
        },
      }}
      {...props}
    />
  )
})

export function Cards ({
  localList,
  field,
  foreignList,
  id,
  value,
  onChange,
  forceValidation,
}: {
  foreignList: ListMeta
  localList: ListMeta
  id: string | null
  value: { kind: 'cards-view' }
} & FieldProps<typeof controller>) {
  const { displayOptions } = value
  let selectedFields = [
    ...new Set([...displayOptions.cardFields, ...(displayOptions.inlineEdit?.fields || [])]),
  ]
    .map(fieldPath => {
      return foreignList.fields[fieldPath].controller.graphqlSelection
    })
    .join('\n')
  if (!displayOptions.cardFields.includes('id')) {
    selectedFields += '\nid'
  }
  if (
    !displayOptions.cardFields.includes(foreignList.labelField) &&
    foreignList.labelField !== 'id'
  ) {
    selectedFields += `\n${foreignList.labelField}`
  }

  const {
    items,
    setItems,
    state: itemsState,
  } = useItemState({
    selectedFields,
    localList,
    id,
    field,
  })

  const client = useApolloClient()

  const [isLoadingLazyItems, setIsLoadingLazyItems] = useState(false)
  const [showConnectItems, setShowConnectItems] = useState(false)
  const [hideConnectItemsLabel, setHideConnectItemsLabel] = useState<'Cancel' | 'Done'>('Cancel')
  const editRef = useRef<HTMLDivElement | null>(null)

  const isMountedRef = useRef(false)
  useEffect(() => {
    isMountedRef.current = true
    return () => {
      isMountedRef.current = false
    }
  })

  useEffect(() => {
    if (value.itemsBeingEdited) {
      editRef?.current?.focus()
    }
  }, [value])

  if (itemsState.kind === 'loading') {
    return (
      <div>
        <LoadingDots label={`Loading items for ${field.label} field`} />
      </div>
    )
  }
  if (itemsState.kind === 'error') {
    return <span css={{ color: 'red' }}>{itemsState.message}</span>
  }

  const currentIdsArrayWithFetchedItems = [...value.currentIds]
    .map(id => ({ itemGetter: items[id], id }))
    .filter(x => x.itemGetter)

  return (
    <Stack gap="medium">
      {currentIdsArrayWithFetchedItems.length !== 0 && (
        <Stack
          as="ul"
          gap="medium"
          css={{
            padding: 0,
            margin: 0,
            li: {
              listStyle: 'none',
            },
          }}
        >
          {currentIdsArrayWithFetchedItems.map(({ id, itemGetter }, index) => {
            const isEditMode = !!(onChange !== undefined) && value.itemsBeingEdited.has(id)
            return (
              <CardContainer role="status" mode={isEditMode ? 'edit' : 'view'} key={id}>
                <VisuallyHidden as="h2">{`${field.label} ${index + 1} ${
                  isEditMode ? 'edit' : 'view'
                } mode`}</VisuallyHidden>
                {isEditMode ? (
                  <InlineEdit
                    list={foreignList}
                    fields={displayOptions.inlineEdit!.fields}
                    onSave={newItemGetter => {
                      setItems({
                        ...items,
                        [id]: newItemGetter,
                      })
                      const itemsBeingEdited = new Set(value.itemsBeingEdited)
                      itemsBeingEdited.delete(id)
                      onChange!({
                        ...value,
                        itemsBeingEdited,
                      })
                    }}
                    selectedFields={selectedFields}
                    itemGetter={itemGetter}
                    onCancel={() => {
                      const itemsBeingEdited = new Set(value.itemsBeingEdited)
                      itemsBeingEdited.delete(id)
                      onChange!({
                        ...value,
                        itemsBeingEdited,
                      })
                    }}
                  />
                ) : (
                  <Stack gap="xlarge">
                    {displayOptions.cardFields.map(fieldPath => {
                      const field = foreignList.fields[fieldPath]
                      const itemForField: Record<string, any> = {}
                      for (const graphqlField of getRootGraphQLFieldsFromFieldController(
                        field.controller
                      )) {
                        const fieldGetter = itemGetter.get(graphqlField)
                        if (fieldGetter.errors) {
                          const errorMessage = fieldGetter.errors[0].message
                          return (
                            <FieldContainer>
                              <FieldLabel>{field.label}</FieldLabel>
                              {errorMessage}
                            </FieldContainer>
                          )
                        }
                        itemForField[graphqlField] = fieldGetter.data
                      }
                      return (
                        <field.views.CardValue
                          key={fieldPath}
                          field={field.controller}
                          item={itemForField}
                        />
                      )
                    })}
                    <Stack across gap="small">
                      {displayOptions.inlineEdit && onChange !== undefined && (
                        <Button
                          size="small"
                          disabled={onChange === undefined}
                          onClick={() => {
                            onChange({
                              ...value,
                              itemsBeingEdited: new Set([...value.itemsBeingEdited, id]),
                            })
                          }}
                          tone="active"
                        >
                          Edit
                        </Button>
                      )}
                      {displayOptions.removeMode === 'disconnect' && onChange !== undefined && (
                        <Tooltip content="This item will not be deleted. It will only be removed from this field.">
                          {props => (
                            <Button
                              size="small"
                              disabled={onChange === undefined}
                              onClick={() => {
                                const currentIds = new Set(value.currentIds)
                                currentIds.delete(id)
                                onChange({
                                  ...value,
                                  currentIds,
                                })
                              }}
                              {...props}
                              tone="negative"
                            >
                              Remove
                            </Button>
                          )}
                        </Tooltip>
                      )}
                      {displayOptions.linkToItem && (
                        <Button
                          size="small"
                          weight="link"
                          tone="active"
                          css={{ textDecoration: 'none' }}
                          as={Link}
                          href={`/${foreignList.path}/${id}`}
                        >
                          View {foreignList.singular} details
                        </Button>
                      )}
                    </Stack>
                  </Stack>
                )}
              </CardContainer>
            )
          })}
        </Stack>
      )}
      {onChange === undefined ? null : displayOptions.inlineConnect && showConnectItems ? (
        <CardContainer mode="edit">
          <Stack
            gap="small"
            across
            css={{
              width: '100%',
              justifyContent: 'space-between',
              'div:first-of-type': {
                flex: '2',
              },
            }}
          >
            <RelationshipSelect
              autoFocus
              controlShouldRenderValue={isLoadingLazyItems}
              isDisabled={onChange === undefined}
              list={foreignList}
              labelField={field.refLabelField}
              searchFields={field.refSearchFields}
              isLoading={isLoadingLazyItems}
              placeholder={`Select a ${foreignList.singular}`}
              portalMenu
              state={{
                kind: 'many',
                async onChange (options) {
                  // TODO: maybe use the extraSelection prop on RelationshipSelect here
                  const itemsToFetchAndConnect: string[] = []
                  options.forEach(item => {
                    if (!value.currentIds.has(item.id)) {
                      itemsToFetchAndConnect.push(item.id)
                    }
                  })
                  if (itemsToFetchAndConnect.length) {
                    try {
                      const { data, errors } = await client.query({
                        query: gql`query ($ids: [ID!]!) {
                      items: ${foreignList.gqlNames.listQueryName}(where: { id: { in: $ids }}) {
                        ${selectedFields}
                      }
                    }`,
                        variables: { ids: itemsToFetchAndConnect },
                      })
                      if (isMountedRef.current) {
                        const dataGetters = makeDataGetter(data, errors)
                        const itemsDataGetter = dataGetters.get('items')
                        const newItems = { ...items }
                        const newCurrentIds = field.many
                          ? new Set(value.currentIds)
                          : new Set<string>()
                        if (Array.isArray(itemsDataGetter.data)) {
                          itemsDataGetter.data.forEach((item, i) => {
                            if (item?.id != null) {
                              newCurrentIds.add(item.id)
                              newItems[item.id] = itemsDataGetter.get(i)
                            }
                          })
                        }
                        if (newCurrentIds.size) {
                          setItems(newItems)
                          onChange({
                            ...value,
                            currentIds: newCurrentIds,
                          })
                          setHideConnectItemsLabel('Done')
                        }
                      }
                    } finally {
                      if (isMountedRef.current) {
                        setIsLoadingLazyItems(false)
                      }
                    }
                  }
                },
                value: (() => {
                  const options: { label: string, id: string }[] = []
                  Object.keys(items).forEach(id => {
                    if (value.currentIds.has(id)) {
                      options.push({ id, label: id })
                    }
                  })
                  return options
                })(),
              }}
            />
            <Button onClick={() => setShowConnectItems(false)}>{hideConnectItemsLabel}</Button>
          </Stack>
        </CardContainer>
      ) : value.itemBeingCreated ? (
        <CardContainer mode="create">
          <InlineCreate
            selectedFields={selectedFields}
            fields={displayOptions.inlineCreate!.fields}
            list={foreignList}
            onCancel={() => {
              onChange({ ...value, itemBeingCreated: false })
            }}
            onCreate={itemGetter => {
              const id = itemGetter.data.id
              setItems({ ...items, [id]: itemGetter })
              onChange({
                ...value,
                itemBeingCreated: false,
                currentIds: field.many ? new Set([...value.currentIds, id]) : new Set([id]),
              })
            }}
          />
        </CardContainer>
      ) : displayOptions.inlineCreate || displayOptions.inlineConnect ? (
        <CardContainer mode="create">
          <Stack gap="small" across>
            {displayOptions.inlineCreate && (
              <Button
                size="small"
                disabled={onChange === undefined}
                tone="positive"
                onClick={() => {
                  onChange({
                    ...value,
                    itemBeingCreated: true,
                  })
                }}
              >
                Create {foreignList.singular}
              </Button>
            )}
            {displayOptions.inlineConnect && (
              <Button
                size="small"
                weight="none"
                tone="passive"
                onClick={() => {
                  setShowConnectItems(true)
                  setHideConnectItemsLabel('Cancel')
                }}
              >
                Link existing {foreignList.singular}
              </Button>
            )}
          </Stack>
        </CardContainer>
      ) : null}
      {/* TODO: this may not be visible to the user when they invoke the save action. Maybe scroll to it? */}
      {forceValidation && (
        <Text color="red600" size="small">
          You must finish creating and editing any related {foreignList.label.toLowerCase()} before
          saving the {localList.singular.toLowerCase()}
        </Text>
      )}
    </Stack>
  )
}
</file>

<file path="cards/InlineCreate.tsx">
/** @jsxRuntime classic */
/** @jsx jsx */

import { useState } from 'react'
import { jsx, Stack } from '@keystone-ui/core'
import isDeepEqual from 'fast-deep-equal'
import { useToasts } from '@keystone-ui/toast'
import { Button } from '@keystone-ui/button'
import { type ListMeta } from '../../../../../types'
import {
  type ItemData,
  makeDataGetter,
  type DataGetter,
  type Value,
  useInvalidFields,
  serializeValueToObjByFieldKey,
  Fields,
} from '../../../../../admin-ui/utils'
import { gql, useMutation } from '../../../../../admin-ui/apollo'
import { GraphQLErrorNotice } from '../../../../../admin-ui/components'
import { useFieldsObj } from './useItemState'

export function InlineCreate ({
  list,
  onCancel,
  onCreate,
  fields: fieldPaths,
  selectedFields,
}: {
  list: ListMeta
  selectedFields: string
  fields: readonly string[]
  onCancel: () => void
  onCreate: (itemGetter: DataGetter<ItemData>) => void
}) {
  const toasts = useToasts()
  const fields = useFieldsObj(list, fieldPaths)

  const [createItem, { loading, error }] = useMutation(
    gql`mutation($data: ${list.gqlNames.createInputName}!) {
      item: ${list.gqlNames.createMutationName}(data: $data) {
        ${selectedFields}
    }
  }`
  )

  const [value, setValue] = useState(() => {
    const value: Value = {}
    Object.keys(fields).forEach(fieldPath => {
      value[fieldPath] = { kind: 'value', value: fields[fieldPath].controller.defaultValue }
    })
    return value
  })

  const invalidFields = useInvalidFields(fields, value)
  const [forceValidation, setForceValidation] = useState(false)

  const onSubmit = () => {
    const newForceValidation = invalidFields.size !== 0
    setForceValidation(newForceValidation)

    if (newForceValidation) return
    const data: Record<string, any> = {}
    const allSerializedValues = serializeValueToObjByFieldKey(fields, value)
    Object.keys(allSerializedValues).forEach(fieldPath => {
      const { controller } = fields[fieldPath]
      const serialized = allSerializedValues[fieldPath]
      if (!isDeepEqual(serialized, controller.serialize(controller.defaultValue))) {
        Object.assign(data, serialized)
      }
    })

    createItem({
      variables: {
        data,
      },
    })
      .then(({ data, errors }) => {
        // we're checking for path.length === 1 because errors with a path larger than 1 will be field level errors
        // which are handled seperately and do not indicate a failure to update the item
        const error = errors?.find(x => x.path?.length === 1)
        if (error) {
          toasts.addToast({
            title: 'Failed to create item',
            tone: 'negative',
            message: error.message,
          })
        } else {
          toasts.addToast({
            title: data.item[list.labelField] || data.item.id,
            tone: 'positive',
            message: 'Saved successfully',
          })
          onCreate(makeDataGetter(data, errors).get('item'))
        }
      })
      .catch(err => {
        toasts.addToast({
          title: 'Failed to update item',
          tone: 'negative',
          message: err.message,
        })
      })
  }

  return (
    <section>
      <Stack gap="xlarge">
        {error && (
          <GraphQLErrorNotice networkError={error?.networkError} errors={error?.graphQLErrors} />
        )}
        <Fields
          fields={fields}
          forceValidation={forceValidation}
          invalidFields={invalidFields}
          onChange={setValue}
          value={value}
        />
        <Stack gap="small" across>
          <Button onClick={onSubmit} isLoading={loading} size="small" tone="positive" weight="bold">
            Create {list.singular}
          </Button>
          <Button size="small" weight="none" onClick={onCancel}>
            Cancel
          </Button>
        </Stack>
      </Stack>
    </section>
  )
}
</file>

<file path="cards/InlineEdit.tsx">
/** @jsxRuntime classic */
/** @jsx jsx */

import { Button } from '@keystone-ui/button'
import { jsx, Stack } from '@keystone-ui/core'
import { useToasts } from '@keystone-ui/toast'
import { useCallback, useState } from 'react'
import { type ListMeta } from '../../../../../types'
import {
  deserializeValue,
  type ItemData,
  useInvalidFields,
  Fields,
  useChangedFieldsAndDataForUpdate,
  makeDataGetter,
  type DataGetter,
} from '../../../../../admin-ui/utils'
import { gql, useMutation } from '../../../../../admin-ui/apollo'
import { GraphQLErrorNotice } from '../../../../../admin-ui/components'
import { useFieldsObj } from './useItemState'

export function InlineEdit ({
  fields,
  list,
  selectedFields,
  itemGetter,
  onCancel,
  onSave,
}: {
  fields: readonly string[]
  list: ListMeta
  selectedFields: string
  itemGetter: DataGetter<ItemData>
  onCancel: () => void
  onSave: (newItemGetter: DataGetter<ItemData>) => void
}) {
  const fieldsObj = useFieldsObj(list, fields)

  const [update, { loading, error }] = useMutation(
    gql`mutation ($data: ${list.gqlNames.updateInputName}!, $id: ID!) {
          item: ${list.gqlNames.updateMutationName}(where: { id: $id }, data: $data) {
            ${selectedFields}
          }
        }`,
    { errorPolicy: 'all' }
  )

  const [state, setValue] = useState(() => {
    const value = deserializeValue(fieldsObj, itemGetter)
    return { value, item: itemGetter.data }
  })

  if (state.item !== itemGetter.data && itemGetter.errors?.every(x => x.path?.length !== 1)) {
    const value = deserializeValue(fieldsObj, itemGetter)
    setValue({ value, item: itemGetter.data })
  }

  const { changedFields, dataForUpdate } = useChangedFieldsAndDataForUpdate(
    fieldsObj,
    itemGetter,
    state.value
  )

  const invalidFields = useInvalidFields(fieldsObj, state.value)

  const [forceValidation, setForceValidation] = useState(false)
  const toasts = useToasts()

  return (
    <form
      onSubmit={event => {
        event.preventDefault()
        if (changedFields.size === 0) {
          onCancel()
          return
        }
        const newForceValidation = invalidFields.size !== 0
        setForceValidation(newForceValidation)
        if (newForceValidation) return

        update({
          variables: {
            data: dataForUpdate,
            id: itemGetter.get('id').data,
          },
        })
          .then(({ data, errors }) => {
            // we're checking for path.length === 1 because errors with a path larger than 1 will be field level errors
            // which are handled seperately and do not indicate a failure to update the item
            const error = errors?.find(x => x.path?.length === 1)
            if (error) {
              toasts.addToast({
                title: 'Failed to update item',
                tone: 'negative',
                message: error.message,
              })
            } else {
              toasts.addToast({
                title: data.item[list.labelField] || data.item.id,
                tone: 'positive',
                message: 'Saved successfully',
              })
              onSave(makeDataGetter(data, errors).get('item'))
            }
          })
          .catch(err => {
            toasts.addToast({
              title: 'Failed to update item',
              tone: 'negative',
              message: err.message,
            })
          })
      }}
    >
      <Stack gap="xlarge">
        {error && (
          <GraphQLErrorNotice
            networkError={error?.networkError}
            // we're checking for path.length === 1 because errors with a path larger than 1 will be field level errors
            // which are handled seperately and do not indicate a failure to update the item
            errors={error?.graphQLErrors.filter(x => x.path?.length === 1)}
          />
        )}
        <Fields
          fields={fieldsObj}
          forceValidation={forceValidation}
          invalidFields={invalidFields}
          onChange={useCallback(
            value => {
              setValue(state => ({ item: state.item, value: value(state.value) }))
            },
            [setValue]
          )}
          value={state.value}
        />
        <Stack across gap="small">
          <Button isLoading={loading} weight="bold" size="small" tone="active" type="submit">
            Save
          </Button>
          <Button size="small" weight="none" onClick={onCancel}>
            Cancel
          </Button>
        </Stack>
      </Stack>
    </form>
  )
}
</file>

<file path="cards/useItemState.tsx">
import { useCallback, useMemo, useState } from 'react'
import { type FieldMeta, type ListMeta } from '../../../../../types'
import { type DataGetter, makeDataGetter } from '../../../../../admin-ui/utils'
import { gql, useQuery } from '../../../../../admin-ui/apollo'
import { type controller } from '../index'

type ItemsState =
  | { kind: 'loading' }
  | { kind: 'error', message: string }
  | { kind: 'loaded' }

type Items = Record<string, DataGetter<{ id: string, [key: string]: any }>>

export function useItemState ({
  selectedFields,
  localList,
  id,
  field,
}: {
  selectedFields: string
  localList: ListMeta
  field: ReturnType<typeof controller>
  id: string | null
}) {
  const { data, error, loading } = useQuery(
    gql`query($id: ID!) {
  item: ${localList.gqlNames.itemQueryName}(where: {id: $id}) {
    id
    relationship: ${field.path} {
      ${selectedFields}
    }
  }
}`,
    { variables: { id }, errorPolicy: 'all', skip: id === null }
  )
  const { itemsArrFromData, relationshipGetter } = useMemo(() => {
    const dataGetter = makeDataGetter(data, error?.graphQLErrors)
    const relationshipGetter = dataGetter.get('item').get('relationship')
    const isMany = Array.isArray(relationshipGetter.data)
    const itemsArrFromData: DataGetter<{ id: string, [key: string]: any }>[] = (
      isMany
        ? relationshipGetter.data.map((_: any, i: number) => relationshipGetter.get(i))
        : [relationshipGetter]
    ).filter((x: DataGetter<any>) => x.data?.id != null)
    return { relationshipGetter, itemsArrFromData }
  }, [data, error])

  let [{ items, itemsArrFromData: itemsArrFromDataState }, setItemsState] = useState<{
    itemsArrFromData: DataGetter<any>[]
    items: Record<
      string,
      {
        current: DataGetter<{ id: string, [key: string]: any }>
        fromInitialQuery: DataGetter<{ id: string, [key: string]: any }> | undefined
      }
    >
  }>({ itemsArrFromData: [], items: {} })

  if (itemsArrFromDataState !== itemsArrFromData) {
    const newItems: Record<
      string,
      {
        current: DataGetter<{ id: string, [key: string]: any }>
        fromInitialQuery: DataGetter<{ id: string, [key: string]: any }> | undefined
      }
    > = {}

    for (const item of itemsArrFromData) {
      const initialItemInState = items[item.data.id]?.fromInitialQuery
      if (
        ((items[item.data.id] && initialItemInState) || !items[item.data.id]) &&
        (!initialItemInState ||
          item.data !== initialItemInState.data ||
          item.errors?.length !== initialItemInState.errors?.length ||
          (item.errors || []).some((err, i) => err !== initialItemInState.errors?.[i]))
      ) {
        newItems[item.data.id] = { current: item, fromInitialQuery: item }
      } else {
        newItems[item.data.id] = items[item.data.id]
      }
    }

    items = newItems
    setItemsState({
      items: newItems,
      itemsArrFromData,
    })
  }

  return {
    items: useMemo(() => {
      const itemsToReturn: Items = {}
      Object.keys(items).forEach(id => {
        itemsToReturn[id] = items[id].current
      })
      return itemsToReturn
    }, [items]),
    setItems: useCallback(
      (items: Items) => {
        setItemsState(state => {
          const itemsForState: (typeof state)['items'] = {}
          Object.keys(items).forEach(id => {
            if (items[id] === state.items[id]?.current) {
              itemsForState[id] = state.items[id]
            } else {
              itemsForState[id] = {
                current: items[id],
                fromInitialQuery: state.items[id]?.fromInitialQuery,
              }
            }
          })
          return {
            itemsArrFromData: state.itemsArrFromData,
            items: itemsForState,
          }
        })
      },
      [setItemsState]
    ),
    state: ((): ItemsState => {
      if (id === null) {
        return { kind: 'loaded' }
      }
      if (loading) {
        return { kind: 'loading' }
      }
      if (error?.networkError) {
        return { kind: 'error', message: error.networkError.message }
      }
      if (field.many && !relationshipGetter.data) {
        return { kind: 'error', message: relationshipGetter.errors?.[0].message || '' }
      }
      return { kind: 'loaded' }
    })(),
  }
}

export function useFieldsObj (list: ListMeta, fields: readonly string[] | undefined) {
  return useMemo(() => {
    const editFields: Record<string, FieldMeta> = {}
    fields?.forEach(fieldPath => {
      editFields[fieldPath] = list.fields[fieldPath]
    })
    return editFields
  }, [fields, list.fields])
}
</file>

<file path="index.tsx">
/** @jsxRuntime classic */
/** @jsx jsx */

import { Fragment, useState } from 'react'

import { Button } from '@keystone-ui/button'
import { jsx, Stack, useTheme } from '@keystone-ui/core'
import { FieldContainer, FieldDescription, FieldLabel, FieldLegend } from '@keystone-ui/fields'
import { DrawerController } from '@keystone-ui/modals'
import {
  type CardValueComponent,
  type CellComponent,
  type FieldController,
  type FieldControllerConfig,
  type FieldProps,
  type ListMeta,
} from '../../../../types'
import { Link } from '../../../../admin-ui/router'
import { useKeystone, useList } from '../../../../admin-ui/context'
import { gql, useQuery } from '../../../../admin-ui/apollo'
import { CellContainer, CreateItemDrawer } from '../../../../admin-ui/components'

import { Cards } from './cards'
import { RelationshipSelect } from './RelationshipSelect'

function LinkToRelatedItems ({
  itemId,
  value,
  list,
  refFieldKey,
}: {
  itemId: string | null
  value: FieldProps<typeof controller>['value'] & { kind: 'many' | 'one' }
  list: ListMeta
  refFieldKey?: string
}) {
  function constructQuery ({
    refFieldKey,
    itemId,
    value,
  }: {
    refFieldKey?: string
    itemId: string | null
    value: FieldProps<typeof controller>['value'] & { kind: 'many' | 'one' }
  }) {
    if (!!refFieldKey && itemId) {
      return `!${refFieldKey}_matches="${itemId}"`
    }
    return `!id_in="${(value?.value as { id: string, label: string }[])
      .slice(0, 100)
      .map(({ id }: { id: string }) => id)
      .join(',')}"`
  }
  const commonProps = {
    size: 'small',
    tone: 'active',
    weight: 'link',
  } as const

  if (value.kind === 'many') {
    const query = constructQuery({ refFieldKey, value, itemId })
    return (
      <Button {...commonProps} as={Link} href={`/${list.path}?${query}`}>
        View related {list.plural}
      </Button>
    )
  }

  return (
    <Button {...commonProps} as={Link} href={`/${list.path}/${value.value?.id}`}>
      View {list.singular} details
    </Button>
  )
}

export const Field = ({
  field,
  value,
  itemValue,
  autoFocus,
  onChange,
  forceValidation,
}: FieldProps<typeof controller>) => {
  const keystone = useKeystone()
  const foreignList = useList(field.refListKey)
  const localList = useList(field.listKey)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)

  if (value.kind === 'cards-view') {
    return (
      <FieldContainer as="fieldset">
        <FieldLegend>{field.label}</FieldLegend>
        <FieldDescription id={`${field.path}-description`}>{field.description}</FieldDescription>
        <Cards
          forceValidation={forceValidation}
          field={field}
          id={value.id}
          value={value}
          itemValue={itemValue}
          onChange={onChange}
          foreignList={foreignList}
          localList={localList}
        />
      </FieldContainer>
    )
  }

  if (value.kind === 'count') {
    return (
      <Stack as="fieldset" gap="medium">
        <FieldLegend>{field.label}</FieldLegend>
        <FieldDescription id={`${field.path}-description`}>{field.description}</FieldDescription>
        <div>
          {value.count === 1
            ? `There is 1 ${foreignList.singular} `
            : `There are ${value.count} ${foreignList.plural} `}
          linked to this {localList.singular}
        </div>
      </Stack>
    )
  }

  const authenticatedItem = keystone.authenticatedItem

  return (
    <FieldContainer as="fieldset">
      <FieldLabel as="legend">{field.label}</FieldLabel>
      <FieldDescription id={`${field.path}-description`}>{field.description}</FieldDescription>
      <Fragment>
        <Stack gap="medium">
          <RelationshipSelect
            controlShouldRenderValue
            aria-describedby={field.description === null ? undefined : `${field.path}-description`}
            autoFocus={autoFocus}
            isDisabled={onChange === undefined}
            labelField={field.refLabelField}
            searchFields={field.refSearchFields}
            list={foreignList}
            portalMenu
            state={
              value.kind === 'many'
                ? {
                    kind: 'many',
                    value: value.value,
                    onChange (newItems) {
                      onChange?.({
                        ...value,
                        value: newItems,
                      })
                    },
                  }
                : {
                    kind: 'one',
                    value: value.value,
                    onChange (newVal) {
                      if (value.kind === 'one') {
                        onChange?.({
                          ...value,
                          value: newVal,
                        })
                      }
                    },
                  }
            }
          />
          <Stack across gap="small">
            {onChange !== undefined && !field.hideCreate && (
              <Button
                size="small"
                disabled={isDrawerOpen}
                onClick={() => {
                  setIsDrawerOpen(true)
                }}
              >
                Create related {foreignList.singular}
              </Button>
            )}
            {onChange !== undefined &&
              authenticatedItem.state === 'authenticated' &&
              authenticatedItem.listKey === field.refListKey &&
              (value.kind === 'many'
                ? value.value.find(x => x.id === authenticatedItem.id) === undefined
                : value.value?.id !== authenticatedItem.id) && (
                <Button
                  size="small"
                  onClick={() => {
                    const val = {
                      label: authenticatedItem.label,
                      id: authenticatedItem.id,
                    }
                    if (value.kind === 'many') {
                      onChange({
                        ...value,
                        value: [...value.value, val],
                      })
                    } else {
                      onChange({
                        ...value,
                        value: val,
                      })
                    }
                  }}
                >
                  {value.kind === 'many' ? 'Add ' : 'Set as '}
                  {authenticatedItem.label}
                </Button>
              )}
            {!!(value.kind === 'many'
              ? value.value.length
              : value.kind === 'one' && value.value) && (
              <LinkToRelatedItems
                itemId={value.id}
                refFieldKey={field.refFieldKey}
                list={foreignList}
                value={value}
              />
            )}
          </Stack>
        </Stack>
        {onChange !== undefined && (
          <DrawerController isOpen={isDrawerOpen}>
            <CreateItemDrawer
              listKey={foreignList.key}
              onClose={() => {
                setIsDrawerOpen(false)
              }}
              onCreate={val => {
                setIsDrawerOpen(false)
                if (value.kind === 'many') {
                  onChange({
                    ...value,
                    value: [...value.value, val],
                  })
                } else if (value.kind === 'one') {
                  onChange({
                    ...value,
                    value: val,
                  })
                }
              }}
            />
          </DrawerController>
        )}
      </Fragment>
    </FieldContainer>
  )
}

export const Cell: CellComponent<typeof controller> = ({ field, item }) => {
  const list = useList(field.refListKey)
  const { colors } = useTheme()

  if (field.display === 'count') {
    const count = item[`${field.path}Count`] ?? 0
    return (
      <CellContainer>
        {count} {count === 1 ? list.singular : list.plural}
      </CellContainer>
    )
  }

  const data = item[field.path]
  const items = (Array.isArray(data) ? data : [data]).filter(item => item)
  const displayItems = items.length < 5 ? items : items.slice(0, 3)
  const overflow = items.length < 5 ? 0 : items.length - 3
  const styles = {
    color: colors.foreground,
    textDecoration: 'none',

    ':hover': {
      textDecoration: 'underline',
    },
  } as const

  return (
    <CellContainer>
      {displayItems.map((item, index) => (
        <Fragment key={item.id}>
          {index ? ', ' : ''}
          <Link href={`/${list.path}/[id]`} as={`/${list.path}/${item.id}`} css={styles}>
            {item.label || item.id}
          </Link>
        </Fragment>
      ))}
      {overflow ? `, and ${overflow} more` : null}
    </CellContainer>
  )
}

export const CardValue: CardValueComponent<typeof controller> = ({ field, item }) => {
  const list = useList(field.refListKey)
  const data = item[field.path]
  return (
    <FieldContainer>
      <FieldLabel>{field.label}</FieldLabel>
      {(Array.isArray(data) ? data : [data])
        .filter(item => item)
        .map((item, index) => (
          <Fragment key={item.id}>
            {index ? ', ' : ''}
            <Link href={`/${list.path}/[id]`} as={`/${list.path}/${item.id}`}>
              {item.label || item.id}
            </Link>
          </Fragment>
        ))}
    </FieldContainer>
  )
}

type SingleRelationshipValue = {
  kind: 'one'
  id: null | string
  initialValue: { label: string, id: string } | null
  value: { label: string, id: string } | null
}
type ManyRelationshipValue = {
  kind: 'many'
  id: null | string
  initialValue: { label: string, id: string }[]
  value: { label: string, id: string }[]
}
type CardsRelationshipValue = {
  kind: 'cards-view'
  id: null | string
  itemsBeingEdited: ReadonlySet<string>
  itemBeingCreated: boolean
  initialIds: ReadonlySet<string>
  currentIds: ReadonlySet<string>
  displayOptions: CardsDisplayModeOptions
}
type CountRelationshipValue = {
  kind: 'count'
  id: null | string
  count: number
}
type CardsDisplayModeOptions = {
  cardFields: readonly string[]
  linkToItem: boolean
  removeMode: 'disconnect' | 'none'
  inlineCreate: { fields: readonly string[] } | null
  inlineEdit: { fields: readonly string[] } | null
  inlineConnect: boolean
}

type RelationshipController = FieldController<
  ManyRelationshipValue | SingleRelationshipValue | CardsRelationshipValue | CountRelationshipValue,
  string
> & {
  display: 'count' | 'cards-or-select'
  listKey: string
  refListKey: string
  refFieldKey?: string
  refLabelField: string
  refSearchFields: string[]
  hideCreate: boolean
  many: boolean
}

export function controller (
  config: FieldControllerConfig<
    {
      refFieldKey?: string
      refListKey: string
      many: boolean
      hideCreate: boolean
      refLabelField: string
      refSearchFields: string[]
    } & (
      | {
          displayMode: 'select'
        }
      | {
          displayMode: 'cards'
          cardFields: readonly string[]
          linkToItem: boolean
          removeMode: 'disconnect' | 'none'
          inlineCreate: { fields: readonly string[] } | null
          inlineEdit: { fields: readonly string[] } | null
          inlineConnect: boolean
        }
      | {
          displayMode: 'count'
        }
    )
  >
): RelationshipController {
  const cardsDisplayOptions =
    config.fieldMeta.displayMode === 'cards'
      ? {
          cardFields: config.fieldMeta.cardFields,
          inlineCreate: config.fieldMeta.inlineCreate,
          inlineEdit: config.fieldMeta.inlineEdit,
          linkToItem: config.fieldMeta.linkToItem,
          removeMode: config.fieldMeta.removeMode,
          inlineConnect: config.fieldMeta.inlineConnect,
        }
      : undefined

  const refLabelField = config.fieldMeta.refLabelField
  const refSearchFields = config.fieldMeta.refSearchFields

  return {
    refFieldKey: config.fieldMeta.refFieldKey,
    many: config.fieldMeta.many,
    listKey: config.listKey,
    path: config.path,
    label: config.label,
    description: config.description,
    display: config.fieldMeta.displayMode === 'count' ? 'count' : 'cards-or-select',
    refLabelField,
    refSearchFields,
    refListKey: config.fieldMeta.refListKey,
    graphqlSelection:
      config.fieldMeta.displayMode === 'count'
        ? `${config.path}Count`
        : `${config.path} {
              id
              label: ${refLabelField}
            }`,
    hideCreate: config.fieldMeta.hideCreate,
    // note we're not making the state kind: 'count' when ui.displayMode is set to 'count'.
    // that ui.displayMode: 'count' is really just a way to have reasonable performance
    // because our other UIs don't handle relationships with a large number of items well
    // but that's not a problem here since we're creating a new item so we might as well them a better UI
    defaultValue:
      cardsDisplayOptions !== undefined
        ? {
            kind: 'cards-view',
            currentIds: new Set(),
            id: null,
            initialIds: new Set(),
            itemBeingCreated: false,
            itemsBeingEdited: new Set(),
            displayOptions: cardsDisplayOptions,
          }
        : config.fieldMeta.many
        ? {
            id: null,
            kind: 'many',
            initialValue: [],
            value: [],
          }
        : { id: null, kind: 'one', value: null, initialValue: null },
    deserialize: data => {
      if (config.fieldMeta.displayMode === 'count') {
        return { id: data.id, kind: 'count', count: data[`${config.path}Count`] ?? 0 }
      }
      if (cardsDisplayOptions !== undefined) {
        const initialIds = new Set<string>(
          (Array.isArray(data[config.path])
            ? data[config.path]
            : data[config.path]
            ? [data[config.path]]
            : []
          ).map((x: any) => x.id)
        )
        return {
          kind: 'cards-view',
          id: data.id,
          itemsBeingEdited: new Set(),
          itemBeingCreated: false,
          initialIds,
          currentIds: initialIds,
          displayOptions: cardsDisplayOptions,
        }
      }
      if (config.fieldMeta.many) {
        const value = (data[config.path] || []).map((x: any) => ({
          id: x.id,
          label: x.label || x.id,
        }))
        return {
          kind: 'many',
          id: data.id,
          initialValue: value,
          value,
        }
      }
      let value = data[config.path]
      if (value) {
        value = {
          id: value.id,
          label: value.label || value.id,
        }
      }
      return {
        kind: 'one',
        id: data.id,
        value,
        initialValue: value,
      }
    },
    filter: {
      Filter: ({ onChange, value }) => {
        const foreignList = useList(config.fieldMeta.refListKey)
        const { filterValues, loading } = useRelationshipFilterValues({
          value,
          list: foreignList,
        })
        const state: {
          kind: 'many'
          value: { label: string, id: string }[]
          onChange: (newItems: { label: string, id: string }[]) => void
        } = {
          kind: 'many',
          value: filterValues,
          onChange (newItems) {
            onChange(newItems.map(item => item.id).join(','))
          },
        }
        return (
          <RelationshipSelect
            controlShouldRenderValue
            list={foreignList}
            labelField={refLabelField}
            searchFields={refSearchFields}
            isLoading={loading}
            isDisabled={onChange === undefined}
            state={state}
          />
        )
      },
      graphql: ({ value }) => {
        const foreignIds = getForeignIds(value)
        if (config.fieldMeta.many) {
          return {
            [config.path]: {
              some: {
                id: {
                  in: foreignIds,
                },
              },
            },
          }
        }
        return {
          [config.path]: {
            id: {
              in: foreignIds,
            },
          },
        }
      },
      Label ({ value }) {
        const foreignList = useList(config.fieldMeta.refListKey)
        const { filterValues } = useRelationshipFilterValues({
          value,
          list: foreignList,
        })

        if (!filterValues.length) {
          return `has no value`
        }
        if (filterValues.length > 1) {
          const values = filterValues.map((i: any) => i.label).join(', ')
          return `is in [${values}]`
        }
        const optionLabel = filterValues[0].label
        return `is ${optionLabel}`
      },
      types: {
        matches: {
          label: 'Matches',
          initialValue: '',
        },
      },
    },
    validate (value) {
      return (
        value.kind !== 'cards-view' ||
        (value.itemsBeingEdited.size === 0 && !value.itemBeingCreated)
      )
    },
    serialize: state => {
      if (state.kind === 'many') {
        const newAllIds = new Set(state.value.map(x => x.id))
        const initialIds = new Set(state.initialValue.map(x => x.id))
        const disconnect = state.initialValue
          .filter(x => !newAllIds.has(x.id))
          .map(x => ({ id: x.id }))
        const connect = state.value.filter(x => !initialIds.has(x.id)).map(x => ({ id: x.id }))
        if (disconnect.length || connect.length) {
          const output: any = {}

          if (disconnect.length) {
            output.disconnect = disconnect
          }

          if (connect.length) {
            output.connect = connect
          }

          return {
            [config.path]: output,
          }
        }
      } else if (state.kind === 'one') {
        if (state.initialValue && !state.value) {
          return { [config.path]: { disconnect: true } }
        } else if (state.value && state.value.id !== state.initialValue?.id) {
          return {
            [config.path]: {
              connect: {
                id: state.value.id,
              },
            },
          }
        }
      } else if (state.kind === 'cards-view') {
        const disconnect = [...state.initialIds]
          .filter(id => !state.currentIds.has(id))
          .map(id => ({ id }))
        const connect = [...state.currentIds]
          .filter(id => !state.initialIds.has(id))
          .map(id => ({ id }))

        if (config.fieldMeta.many) {
          if (disconnect.length || connect.length) {
            return {
              [config.path]: {
                connect: connect.length ? connect : undefined,
                disconnect: disconnect.length ? disconnect : undefined,
              },
            }
          }
        } else if (connect.length) {
          return {
            [config.path]: {
              connect: connect[0],
            },
          }
        } else if (disconnect.length) {
          return { [config.path]: { disconnect: true } }
        }
      }
      return {}
    },
  }
}

function useRelationshipFilterValues ({ value, list }: { value: string, list: ListMeta }) {
  const foreignIds = getForeignIds(value)
  const where = { id: { in: foreignIds } }

  const query = gql`
    query FOREIGNLIST_QUERY($where: ${list.gqlNames.whereInputName}!) {
      items: ${list.gqlNames.listQueryName}(where: $where) {
        id
        ${list.labelField}
      }
    }
  `

  const { data, loading } = useQuery(query, {
    variables: {
      where,
    },
  })

  return {
    filterValues:
      data?.items?.map((item: any) => {
        return {
          id: item.id,
          label: item[list.labelField] || item.id,
        }
      }) || foreignIds.map(f => ({ label: f, id: f })),
    loading: loading,
  }
}

function getForeignIds (value: string) {
  if (typeof value === 'string' && value.length > 0) {
    return value.split(',')
  }
  return []
}
</file>

<file path="RelationshipSelect.tsx">
/** @jsxRuntime classic */
/** @jsx jsx */

import 'intersection-observer'
import {
  type RefObject,
  useEffect,
  useMemo,
  useState,
  createContext,
  useContext,
  useRef
} from 'react'

import { jsx } from '@keystone-ui/core'
import { MultiSelect, Select, selectComponents } from '@keystone-ui/fields'
import { type ListMeta } from '../../../../types'
import {
  type TypedDocumentNode,
  ApolloClient,
  gql,
  InMemoryCache,
  useApolloClient,
  useQuery,
} from '../../../../admin-ui/apollo'
import {
  useKeystone
} from '../../../../admin-ui/context'

function useIntersectionObserver (cb: IntersectionObserverCallback, ref: RefObject<any>) {
  const cbRef = useRef(cb)
  useEffect(() => {
    cbRef.current = cb
  })
  useEffect(() => {
    const observer = new IntersectionObserver((...args) => cbRef.current(...args), {})
    const node = ref.current
    if (node !== null) {
      observer.observe(node)
      return () => observer.unobserve(node)
    }
  }, [ref])
}

function useDebouncedValue<T> (value: T, limitMs: number) {
  const [debouncedValue, setDebouncedValue] = useState(() => value)

  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedValue(() => value)
    }, limitMs)
    return () => clearTimeout(timeout)
  }, [value, limitMs])

  return debouncedValue
}

function isInt (x: string) {
  return Number.isInteger(Number(x))
}

function isBigInt (x: string) {
  try {
    BigInt(x)
    return true
  } catch {
    return true
  }
}

// TODO: this is unfortunate, remove in breaking change?
function isUuid (x: unknown) {
  if (typeof x !== 'string') return
  if (x.length !== 36) return
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(x)
}

export function useSearchFilter (
  value: string,
  list: ListMeta,
  searchFields: string[],
  lists: {
    [list: string]: ListMeta
  }
) {
  return useMemo(() => {
    const trimmedSearch = value.trim()
    if (!trimmedSearch.length) return { OR: [] }

    const conditions: Record<string, any>[] = []
    const idField = list.fields.id.fieldMeta as { type: string, kind: string }

    if (idField.type === 'String') {
      // TODO: remove in breaking change?
      if (idField.kind === 'uuid') {
        if (isUuid(value)) {
          conditions.push({ id: { equals: trimmedSearch } })
        }
      } else {
        conditions.push({ id: { equals: trimmedSearch } })
      }
    } else if (idField.type === 'Int' && isInt(trimmedSearch)) {
      conditions.push({ id: { equals: Number(trimmedSearch) } })

    } else if (idField.type === 'BigInt' && isBigInt(trimmedSearch)) {
      conditions.push({ id: { equals: trimmedSearch } })
    }

    for (const fieldKey of searchFields) {
      const field = list.fields[fieldKey]

      // @ts-expect-error TODO: fix fieldMeta type for relationship fields
      if (field.fieldMeta?.refSearchFields) {
        const {
          // @ts-expect-error TODO: fix fieldMeta type for relationship fields
          refListKey,
          // @ts-expect-error TODO: fix fieldMeta type for relationship fields
          refSearchFields,
          // @ts-expect-error TODO: fix fieldMeta type for relationship fields
          many = false,
        } = field.fieldMeta
        const refList = lists[refListKey]

        for (const refFieldKey of refSearchFields) {
          const refField = refList.fields[refFieldKey]
          if (!refField.search) continue // WARNING: we dont support depth > 2

          if (many) {
            conditions.push({
              [fieldKey]: {
                some: {
                  [refFieldKey]: {
                    contains: trimmedSearch,
                    mode: refField.search === 'insensitive' ? 'insensitive' : undefined,
                  },
                },
              },
            })

            continue
          }

          conditions.push({
            [fieldKey]: {
              [refFieldKey]: {
                contains: trimmedSearch,
                mode: refField.search === 'insensitive' ? 'insensitive' : undefined,
              },
            },
          })
        }

        continue
      }

      conditions.push({
        [field.path]: {
          contains: trimmedSearch,
          mode: field.search === 'insensitive' ? 'insensitive' : undefined,
        },
      })
    }

    return { OR: conditions }
  }, [value, list, searchFields])
}

const idFieldAlias = '____id____'
const labelFieldAlias = '____label____'

const LoadingIndicatorContext = createContext<{
  count: number
  ref:(element: HTMLElement | null) => void
}>({
  count: 0,
  ref: () => {},
})

export function RelationshipSelect ({
  autoFocus,
  controlShouldRenderValue,
  isDisabled,
  isLoading,
  labelField,
  searchFields,
  list,
  placeholder,
  portalMenu,
  state,
  extraSelection = '',
}: {
  autoFocus?: boolean
  controlShouldRenderValue: boolean
  isDisabled: boolean
  isLoading?: boolean
  labelField: string
  searchFields: string[]
  list: ListMeta
  placeholder?: string
  portalMenu?: true | undefined
  state:
    | {
        kind: 'many'
        value: { label: string, id: string, data?: Record<string, any> }[]
        onChange(value: { label: string, id: string, data: Record<string, any> }[]): void
      }
    | {
        kind: 'one'
        value: { label: string, id: string, data?: Record<string, any> } | null
        onChange(value: { label: string, id: string, data: Record<string, any> } | null): void
      }
  extraSelection?: string
}) {
  const keystone = useKeystone()
  const [search, setSearch] = useState('')
  // note it's important that this is in state rather than a ref
  // because we want a re-render if the element changes
  // so that we can register the intersection observer
  // on the right element
  const [loadingIndicatorElement, setLoadingIndicatorElement] = useState<null | HTMLElement>(null)

  const QUERY: TypedDocumentNode<
    { items: { [idFieldAlias]: string, [labelFieldAlias]: string | null }[], count: number },
    { where: Record<string, any>, take: number, skip: number }
  > = gql`
    query RelationshipSelect($where: ${list.gqlNames.whereInputName}!, $take: Int!, $skip: Int!) {
      items: ${list.gqlNames.listQueryName}(where: $where, take: $take, skip: $skip) {
        ${idFieldAlias}: id
        ${labelFieldAlias}: ${labelField}
        ${extraSelection}
      }
      count: ${list.gqlNames.listQueryCountName}(where: $where)
    }
  `

  const debouncedSearch = useDebouncedValue(search, 200)
  const where = useSearchFilter(debouncedSearch, list, searchFields, keystone.adminMeta.lists)

  const link = useApolloClient().link
  // we're using a local apollo client here because writing a global implementation of the typePolicies
  // would require making assumptions about how pagination should work which won't always be right
  const apolloClient = useMemo(
    () =>
      new ApolloClient({
        link,
        cache: new InMemoryCache({
          typePolicies: {
            Query: {
              fields: {
                [list.gqlNames.listQueryName]: {
                  keyArgs: ['where'],
                  merge: (existing: readonly unknown[], incoming: readonly unknown[], { args }) => {
                    const merged = existing ? existing.slice() : []
                    const { skip } = args!
                    for (let i = 0; i < incoming.length; ++i) {
                      merged[skip + i] = incoming[i]
                    }
                    return merged
                  },
                },
              },
            },
          },
        }),
      }),
    [link, list.gqlNames.listQueryName]
  )

  const initialItemsToLoad = Math.min(list.pageSize, 10)
  const subsequentItemsToLoad = Math.min(list.pageSize, 50)
  const { data, error, loading, fetchMore } = useQuery(QUERY, {
    fetchPolicy: 'network-only',
    variables: { where, take: initialItemsToLoad, skip: 0 },
    client: apolloClient,
  })

  const count = data?.count || 0

  const options =
    data?.items?.map(({ [idFieldAlias]: value, [labelFieldAlias]: label, ...data }) => ({
      value,
      label: label || value,
      data,
    })) || []

  const loadingIndicatorContextVal = useMemo(
    () => ({
      count,
      ref: setLoadingIndicatorElement,
    }),
    [count]
  )

  // we want to avoid fetching more again and `loading` from Apollo
  // doesn't seem to become true when fetching more
  const [lastFetchMore, setLastFetchMore] = useState<{
    where: Record<string, any>
    extraSelection: string
    list: ListMeta
    skip: number
  } | null>(null)

  useIntersectionObserver(
    ([{ isIntersecting }]) => {
      const skip = data?.items.length
      if (
        !loading &&
        skip &&
        isIntersecting &&
        options.length < count &&
        (lastFetchMore?.extraSelection !== extraSelection ||
          lastFetchMore?.where !== where ||
          lastFetchMore?.list !== list ||
          lastFetchMore?.skip !== skip)
      ) {
        const QUERY: TypedDocumentNode<
          { items: { [idFieldAlias]: string, [labelFieldAlias]: string | null }[] },
          { where: Record<string, any>, take: number, skip: number }
        > = gql`
          query RelationshipSelectMore($where: ${list.gqlNames.whereInputName}!, $take: Int!, $skip: Int!) {
            items: ${list.gqlNames.listQueryName}(where: $where, take: $take, skip: $skip) {
              ${labelFieldAlias}: ${labelField}
              ${idFieldAlias}: id
              ${extraSelection}
            }
          }
        `

        setLastFetchMore({ extraSelection, list, skip, where })
        fetchMore({
          query: QUERY,
          variables: {
            where,
            take: subsequentItemsToLoad,
            skip,
          },
        })
          .then(() => {
            setLastFetchMore(null)
          })
          .catch(() => {
            setLastFetchMore(null)
          })
      }
    },
    { current: loadingIndicatorElement }
  )

  // TODO: better error UI
  // TODO: Handle permission errors
  // (ie; user has permission to read this relationship field, but
  // not the related list, or some items on the list)
  if (error) {
    return <span>Error</span>
  }

  if (state.kind === 'one') {
    return (
      <LoadingIndicatorContext.Provider value={loadingIndicatorContextVal}>
        <Select
          // this is necessary because react-select passes a second argument to onInputChange
          // and useState setters log a warning if a second argument is passed
          onInputChange={val => setSearch(val)}
          isLoading={loading || isLoading}
          autoFocus={autoFocus}
          components={relationshipSelectComponents}
          portalMenu={portalMenu}
          value={
            state.value
              ? {
                  value: state.value.id,
                  label: state.value.label,
                  // @ts-expect-error
                  data: state.value.data,
                }
              : null
          }
          options={options}
          onChange={value => {
            state.onChange(
              value
                ? {
                    id: value.value,
                    label: value.label,
                    data: (value as any).data,
                  }
                : null
            )
          }}
          placeholder={placeholder}
          controlShouldRenderValue={controlShouldRenderValue}
          isClearable={controlShouldRenderValue}
          isDisabled={isDisabled}
        />
      </LoadingIndicatorContext.Provider>
    )
  }

  return (
    <LoadingIndicatorContext.Provider value={loadingIndicatorContextVal}>
      <MultiSelect // this is necessary because react-select passes a second argument to onInputChange
        // and useState setters log a warning if a second argument is passed
        onInputChange={val => setSearch(val)}
        isLoading={loading || isLoading}
        autoFocus={autoFocus}
        components={relationshipSelectComponents}
        portalMenu={portalMenu}
        value={state.value.map(value => ({
          value: value.id,
          label: value.label,
          data: value.data,
        }))}
        options={options}
        onChange={value => {
          state.onChange(value.map(x => ({ id: x.value, label: x.label, data: (x as any).data })))
        }}
        placeholder={placeholder}
        controlShouldRenderValue={controlShouldRenderValue}
        isClearable={controlShouldRenderValue}
        isDisabled={isDisabled}
      />
    </LoadingIndicatorContext.Provider>
  )
}

const relationshipSelectComponents: Partial<typeof selectComponents> = {
  MenuList: ({ children, ...props }) => {
    const { count, ref } = useContext(LoadingIndicatorContext)
    return (
      <selectComponents.MenuList {...props}>
        {children}
        <div css={{ textAlign: 'center' }} ref={ref}>
          {props.options.length < count && <span css={{ padding: 8 }}>Loading...</span>}
        </div>
      </selectComponents.MenuList>
    )
  },
}
</file>

</files>
